package com.develop.mybible.data

import android.content.Context

/**
 * Datos de la Biblia para ser utilizados en la aplicación
 * Esta clase ahora actúa como fachada para acceder a los datos reales de la base de datos
 */
object BibleTestData {
    private var repository: BibleRepository? = null
    
    /**
     * Inicializa el repositorio de datos con el contexto de la aplicación
     */
    fun initialize(context: Context) {
        if (repository == null) {
            repository = BibleRepository(context.applicationContext)
        }
    }

    /**
     * Obtiene los nombres de todos los libros de la Biblia
     */
    fun getBookNames(): List<String> {
        checkRepository()
        return repository?.getBookNames() ?: emptyList()
    }

    /**
     * Obtiene los números de capítulos para un libro dado
     */
    fun getChapterNumbers(book: String): List<String> {
        checkRepository()
        return repository?.getChapterNumbers(book) ?: emptyList()
    }

    /**
     * Obtiene los números de versículos para un libro y capítulo dados
     */
    fun getVerseNumbers(book: String, chapter: String): List<String> {
        checkRepository()
        return repository?.getVerseNumbers(book, chapter) ?: emptyList()
    }

    /**
     * Obtiene todos los versículos para un libro y capítulo dados
     */
    fun getVersesForChapter(book: String, chapter: String): Map<String, String> {
        checkRepository()
        return repository?.getVersesForChapter(book, chapter) ?: emptyMap()
    }

    /**
     * Cierra el repositorio cuando ya no se necesita
     */
    fun closeRepository() {
        repository?.close()
        repository = null
    }

    /**
     * Verifica que el repositorio esté inicializado
     */
    private fun checkRepository() {
        check(repository != null) { "El repositorio no ha sido inicializado. Llama a initialize(context) primero." }
    }
} 