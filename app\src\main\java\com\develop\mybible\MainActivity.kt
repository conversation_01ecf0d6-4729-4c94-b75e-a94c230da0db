package com.develop.mybible

import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.*
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.Bookmark
import androidx.compose.material.icons.outlined.Menu
import androidx.compose.material.icons.outlined.Share
import androidx.compose.material3.*
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.DrawerState
import androidx.compose.material3.ModalDrawerSheet
import androidx.compose.material3.ModalNavigationDrawer
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.SnackbarDuration
import kotlinx.coroutines.launch
import kotlinx.coroutines.CoroutineScope
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextIndent
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.develop.mybible.data.BibleTestData
import com.develop.mybible.ui.theme.MyBibleTheme
import androidx.compose.foundation.BorderStroke
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInParent
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.runtime.key
import kotlinx.coroutines.delay
import com.develop.mybible.audio.TtsManager
import com.develop.mybible.audio.AudioControls
import java.util.Locale
import androidx.compose.ui.platform.LocalContext
import com.develop.mybible.ui.onboarding.OnboardingScreen
import com.develop.mybible.ui.settings.SettingsScreen
import com.develop.mybible.ui.splash.SplashScreen
import android.content.Intent
import android.net.Uri

class MainActivity : ComponentActivity() {

    // Instancia de Text-to-Speech Manager
    private lateinit var ttsManager: TtsManager

    // Instancia de Preferences Manager
    private lateinit var preferencesManager: PreferencesManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Configurar la ventana para evitar problemas con DecorView pero manteniendo el espacio correcto
        window?.decorView?.let { decorView ->
            // En Android 11+ usamos un enfoque diferente para evitar problemas con DecorView
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Usar true para que el contenido se ajuste a los bordes del sistema
                window.setDecorFitsSystemWindows(true)
            }
        }

        // Inicializamos el repositorio de datos con el contexto de la aplicación
        BibleTestData.initialize(applicationContext)

        // Inicializamos el Text-to-Speech Manager
        ttsManager = TtsManager(applicationContext)

        // Inicializamos el Preferences Manager
        preferencesManager = PreferencesManager(applicationContext)

        setContent {
            // Estado para controlar el tema (oscuro/claro), inicializado desde preferencias
            var isDarkTheme by remember { mutableStateOf(preferencesManager.isDarkTheme()) }

            // Estado para seguimiento de selecciones, inicializado con la última posición guardada
            var selectedBook by remember { mutableStateOf(preferencesManager.getLastBook()) }
            var selectedChapter by remember { mutableStateOf(preferencesManager.getLastChapter()) }
            var selectedVerse by remember { mutableStateOf(preferencesManager.getLastVerse()) }

            // Estado para indicar que es la apertura inicial de la app
            var isInitialLaunch by remember { mutableStateOf(true) }

            // Estado para controlar la navegación entre pantallas
            var currentScreen by remember { mutableStateOf("splash") }

            // Variables para el manejo de "presiona dos veces para salir"
            var backPressedTime by remember { mutableStateOf(0L) }
            val snackbarHostState = remember { SnackbarHostState() }
            val scope = rememberCoroutineScope()

            // Configurar el manejo del botón de atrás
            BackHandler(enabled = true) {
                when (currentScreen) {
                    "settings" -> {
                        // Si estamos en la pantalla de ajustes, volvemos a la pantalla principal
                        currentScreen = "main"
                        // Forzar el desplazamiento al versículo seleccionado
                        isInitialLaunch = true
                    }
                    "main" -> {
                        // Implementación de "presiona dos veces para salir"
                        val currentTime = System.currentTimeMillis()

                        // Si es la primera vez que presiona o ha pasado más de 2 segundos
                        if (backPressedTime == 0L || currentTime - backPressedTime > 2000) {
                            // Actualizar el tiempo de la última pulsación
                            backPressedTime = currentTime

                            // Mostrar mensaje con Snackbar
                            scope.launch {
                                snackbarHostState.showSnackbar(
                                    message = "Presiona 2 veces para salir",
                                    duration = SnackbarDuration.Short
                                )
                            }
                        } else {
                            // Si es la segunda pulsación en menos de 2 segundos, salir de la app
                            backPressedTime = 0L
                            finish()
                        }
                    }
                }
            }

            MyBibleTheme(darkTheme = isDarkTheme) {
                // Scaffold para mostrar el Snackbar
                Scaffold(
                    snackbarHost = { SnackbarHost(hostState = snackbarHostState) }
                ) { paddingValues ->
                    Box(modifier = Modifier.padding(paddingValues)) {
                        when (currentScreen) {
                    "splash" -> {
                        SplashScreen(
                            onSplashFinished = {
                                // Al terminar el splash, decidimos si mostrar onboarding o la pantalla principal
                                if (!preferencesManager.isOnboardingCompleted()) {
                                    currentScreen = "onboarding"
                                } else {
                                    currentScreen = "main"
                                }
                            }
                        )
                    }
                    "onboarding" -> {
                        OnboardingScreen(
                            onFinishOnboarding = {
                                // Marcar onboarding como completado y mostrar la pantalla principal
                                preferencesManager.saveOnboardingState(true)
                                currentScreen = "main"
                            }
                        )
                    }
                    "settings" -> {
                        SettingsScreen(
                            onBackPressed = {
                                // Al volver de ajustes, marcamos que debemos desplazarnos al versículo seleccionado
                                // Esto forzará el desplazamiento al versículo actual
                                isInitialLaunch = true
                                currentScreen = "main"
                            },
                            isDarkTheme = isDarkTheme,
                            onThemeChange = { newTheme ->
                                isDarkTheme = newTheme
                                // Guardar la preferencia de tema
                                preferencesManager.setDarkTheme(newTheme)
                            },
                            preferencesManager = preferencesManager
                        )
                    }
                    "main" -> {
                        // Mostrar la pantalla principal de la aplicación
                        MainScreen(
                            isDarkTheme = isDarkTheme,
                            onThemeChange = { newTheme ->
                                isDarkTheme = newTheme
                                // Guardar la preferencia de tema
                                preferencesManager.setDarkTheme(newTheme)
                            },
                            selectedBook = selectedBook,
                            selectedChapter = selectedChapter,
                            selectedVerse = selectedVerse,
                            isInitialLaunch = isInitialLaunch,
                            onInitialScrollComplete = {
                                // Una vez completado el desplazamiento inicial, marcar que ya no es lanzamiento inicial
                                isInitialLaunch = false
                            },
                            onBookSelected = {
                                selectedBook = it
                                // Guardar la nueva posición de lectura
                                preferencesManager.saveLastReadingPosition(selectedBook, selectedChapter, selectedVerse)
                            },
                            onChapterSelected = {
                                selectedChapter = it
                                // Guardar la nueva posición de lectura
                                preferencesManager.saveLastReadingPosition(selectedBook, selectedChapter, selectedVerse)
                            },
                            onVerseSelected = {
                                selectedVerse = it
                                // Guardar la nueva posición de lectura
                                preferencesManager.saveLastReadingPosition(selectedBook, selectedChapter, selectedVerse)
                            },
                            onNavigateToSettings = {
                                currentScreen = "settings"
                            },
                            ttsManager = ttsManager,
                            preferencesManager = preferencesManager
                        )
                    }
                        }
                    }
                }
            }
        }
    }

    override fun onPause() {
        super.onPause()
        // Guardar el estado actual de TTS
        ttsManager.getCurrentSpeed().let { preferencesManager.setSpeechRate(it) }
        ttsManager.getCurrentPitch().let { preferencesManager.setSpeechPitch(it) }
        Log.d("MainActivity", "onPause: Guardando estado actual: velocidad=${ttsManager.getCurrentSpeed()}, tono=${ttsManager.getCurrentPitch()}")

        // Detener reproducción cuando la app pasa a segundo plano
        ttsManager.stopPlaybackIfActive()
        Log.d("MainActivity", "onPause: Deteniendo reproducción si está activa")
    }

    override fun onStop() {
        super.onStop()
        // Asegurarse de que la reproducción se detenga cuando la app no es visible
        ttsManager.stopPlaybackIfActive()
        Log.d("MainActivity", "onStop: Deteniendo reproducción si está activa")
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            // Detener cualquier reproducción en curso
            if (ttsManager.isPlaying()) {
                ttsManager.stop()
            }

            // Guardar el estado final
            ttsManager.getCurrentSpeed().let { preferencesManager.setSpeechRate(it) }
            ttsManager.getCurrentPitch().let { preferencesManager.setSpeechPitch(it) }

            // Liberar recursos
            ttsManager.shutdown()
            Log.d("MainActivity", "onDestroy: Recursos TTS liberados correctamente")

            // Cerramos el repositorio cuando la actividad se destruye
            BibleTestData.closeRepository()
        } catch (e: Exception) {
            Log.e("MainActivity", "Error al liberar recursos TTS", e)
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    isDarkTheme: Boolean,
    onThemeChange: (Boolean) -> Unit,
    selectedBook: String,
    selectedChapter: String,
    selectedVerse: String,
    isInitialLaunch: Boolean,
    onInitialScrollComplete: () -> Unit,
    onBookSelected: (String) -> Unit,
    onChapterSelected: (String) -> Unit,
    onVerseSelected: (String) -> Unit,
    onNavigateToSettings: () -> Unit,
    ttsManager: TtsManager,
    preferencesManager: Any
) {
    var showDrawer by remember { mutableStateOf(false) }

    // Variable para controlar si el versículo fue seleccionado explícitamente
    var verseExplicitlySelected by remember { mutableStateOf(false) }

    // Crear un scope para las corrutinas
    val scope = rememberCoroutineScope()

    // Usar ModalNavigationDrawer para envolver todo el contenido
    val drawerState = rememberDrawerState(initialValue = DrawerValue.Closed)

    // Definir el contenido del drawer
    @Composable
    fun DrawerContent() {
        val context = LocalContext.current
        Column(
            modifier = Modifier
                .fillMaxHeight()
                .width(280.dp)
                .padding(16.dp)
        ) {
            Text(
                "OpenBiblia",
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.padding(8.dp)
            )
            Divider()
            Spacer(modifier = Modifier.height(8.dp))

            // Opciones del menú
            DrawerMenuItem(
                icon = Icons.Default.Settings,
                text = "Ajustes",
                onClick = {
                    // Cerrar el drawer y navegar a ajustes
                    scope.launch { drawerState.close() }
                    // Navegar a ajustes (usando la función que se pasa desde MainActivity)
                    onNavigateToSettings()
                }
            )
            DrawerMenuItem(
                icon = Icons.Default.Info,
                text = "Políticas de privacidad",
                onClick = {
                    // Cerrar el drawer
                    scope.launch { drawerState.close() }
                    // Abrir el navegador con la URL de políticas de privacidad
                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://customlink.com"))
                    context.startActivity(intent)
                }
            )
        }
    }

    // Estado para controlar TTS para el botón flotante
    var isPlaying by remember { mutableStateOf(false) }
    var showVoiceSettings by remember { mutableStateOf(false) }

    // Estado para verificar si el TTS tiene voces disponibles
    var isTtsAvailable by remember { mutableStateOf(false) }

    // Observar el estado de procesamiento para el indicador visual
    val isProcessing by ttsManager.isProcessingFlow.collectAsState(initial = false)

    // Observar el estado del TTS
    val ttsState by ttsManager.ttsStateFlow.collectAsState()

    // Cargar valores guardados de preferencias - usar reflection para acceder a los métodos
    val getSpeechRate = try {
        preferencesManager.javaClass.getMethod("getSpeechRate").invoke(preferencesManager) as Float
    } catch (e: Exception) {
        1.0f
    }

    val getSpeechPitch = try {
        preferencesManager.javaClass.getMethod("getSpeechPitch").invoke(preferencesManager) as Float
    } catch (e: Exception) {
        1.0f
    }

    var currentSpeed by remember { mutableStateOf(getSpeechRate) }
    var currentPitch by remember { mutableStateOf(getSpeechPitch) }

    // Función para guardar valores en preferencias usando reflection
    val setSpeechRate = { value: Float ->
        try {
            preferencesManager.javaClass.getMethod("setSpeechRate", Float::class.java).invoke(preferencesManager, value)
        } catch (e: Exception) {
            // No hacer nada si el método no existe
        }
    }

    val setSpeechPitch = { value: Float ->
        try {
            preferencesManager.javaClass.getMethod("setSpeechPitch", Float::class.java).invoke(preferencesManager, value)
        } catch (e: Exception) {
            // No hacer nada si el método no existe
        }
    }

    val setSelectedVoice = { value: String? ->
        try {
            preferencesManager.javaClass.getMethod("setSelectedVoice", String::class.java).invoke(preferencesManager, value)
        } catch (e: Exception) {
            // No hacer nada si el método no existe
        }
    }

    // Configurar TTS con los valores guardados
    LaunchedEffect(Unit) {
        // Cargar valores guardados desde las preferencias
        val savedSpeed = try {
            preferencesManager.javaClass.getMethod("getSpeechRate").invoke(preferencesManager) as Float
        } catch (e: Exception) {
            1.0f
        }

        val savedPitch = try {
            preferencesManager.javaClass.getMethod("getSpeechPitch").invoke(preferencesManager) as Float
        } catch (e: Exception) {
            1.0f
        }

        // Actualizar los valores en la UI
        currentSpeed = savedSpeed
        currentPitch = savedPitch

        Log.d("MainActivity", "Valores cargados desde preferencias: velocidad=$savedSpeed, tono=$savedPitch")

        // Asegurarse de que el TTS esté inicializado antes de aplicar configuraciones
        if (ttsManager.ensureInitialized()) {
            // Aplicar los valores guardados al TTS
            ttsManager.setSpeed(savedSpeed)
            ttsManager.setPitch(savedPitch)
            Log.d("MainActivity", "Aplicando configuraciones guardadas al TTS")
        }
    }

    // Estado para las voces disponibles
    val availableVoiceNames = remember { mutableStateListOf<String>() }
    var selectedVoiceName by remember { mutableStateOf<String?>(null) }
    var expandedVoiceDropdown by remember { mutableStateOf(false) }

    // Estado para mostrar mensajes de error de TTS
    var showTtsError by remember { mutableStateOf(false) }
    var ttsErrorMessage by remember { mutableStateOf("") }

    // Inicializar TTS al inicio
    LaunchedEffect(Unit) {
        Log.d("MainActivity", "Iniciando inicialización de TTS")

        // Inicializar TTS
        ttsManager.ensureInitialized()

        // Añadir un retraso para dar tiempo a la inicialización
        delay(500)

        // Verificar si hay voces disponibles
        isTtsAvailable = ttsManager.areVoicesAvailable()
        Log.d("MainActivity", "TTS disponible: $isTtsAvailable")
    }

    // Observar cambios en el estado del TTS
    LaunchedEffect(ttsState) {
        Log.d("MainActivity", "Estado del TTS actualizado: $ttsState")

        // Actualizar el estado de disponibilidad basado en el estado del TTS
        isTtsAvailable = when (ttsState) {
            TtsManager.TtsState.Ready -> true
            else -> false
        }

        // Mostrar mensaje de error según el estado
        when (ttsState) {
            TtsManager.TtsState.Error -> {
                ttsErrorMessage = "Error al inicializar el sistema de voz."
                showTtsError = true
            }
            TtsManager.TtsState.LanguageNotSupported -> {
                ttsErrorMessage = "El idioma no está disponible en este dispositivo."
                showTtsError = true
            }
            TtsManager.TtsState.NoVoicesAvailable -> {
                ttsErrorMessage = "No hay voces disponibles en este dispositivo."
                showTtsError = true
            }
            else -> {
                // No mostrar error para otros estados
            }
        }
    }

    // Cargar las voces disponibles cuando se inicialice el TTS
    DisposableEffect(Unit) {
        val onVoicesInitialized: (List<android.speech.tts.Voice>) -> Unit = { _voices ->
            // Actualizamos la lista de nombres amigables
            availableVoiceNames.clear()
            val voiceNames = ttsManager.getAvailableVoiceNames()
            availableVoiceNames.addAll(voiceNames)

            // Actualizar el estado de disponibilidad de TTS
            isTtsAvailable = voiceNames.isNotEmpty()
            Log.d("MainActivity", "Voces disponibles cargadas: ${voiceNames.size} voces, TTS disponible: $isTtsAvailable")

            // Obtener la voz guardada o usar la actual
            val savedVoice = try {
                preferencesManager.javaClass.getMethod("getSelectedVoice").invoke(preferencesManager) as? String
            } catch (e: Exception) {
                null
            }

            if (savedVoice != null && availableVoiceNames.contains(savedVoice)) {
                selectedVoiceName = savedVoice
                ttsManager.setVoiceByFriendlyName(savedVoice)
                Log.d("MainActivity", "Voz guardada aplicada: $savedVoice")
            } else {
                selectedVoiceName = ttsManager.getCurrentVoiceFriendlyName()
                Log.d("MainActivity", "Usando voz actual: $selectedVoiceName")
            }
        }

        // Manejar errores de TTS
        val onTtsError = { error: TtsManager.TtsError ->
            val message = when(error) {
                TtsManager.TtsError.INITIALIZATION_FAILED -> "No se pudo inicializar el sistema de voz."
                TtsManager.TtsError.LANGUAGE_UNAVAILABLE -> "El idioma español no está disponible en este dispositivo."
                TtsManager.TtsError.NO_VOICES_AVAILABLE -> "No hay voces disponibles en este dispositivo."
                TtsManager.TtsError.PLAYBACK_ERROR -> "Error al reproducir el texto."
                TtsManager.TtsError.NETWORK_UNAVAILABLE -> "La voz seleccionada requiere conexión a internet, pero no hay conexión disponible."
                TtsManager.TtsError.UNKNOWN_ERROR -> "Error desconocido en el sistema de voz."
            }
            ttsErrorMessage = message
            showTtsError = true
        }

        ttsManager.onVoicesInitialized = onVoicesInitialized
        ttsManager.onTtsError = onTtsError

        onDispose {
            ttsManager.onVoicesInitialized = null
            ttsManager.onTtsError = null
        }
    }

    // Observar el estado de reproducción del TTS a nivel de pantalla principal
    DisposableEffect(Unit) {
        ttsManager.onPlaybackStarted = {
            isPlaying = true
        }

        ttsManager.onPlaybackStopped = {
            isPlaying = false
        }

        ttsManager.onPlaybackCompleted = {
            isPlaying = false
        }

        onDispose {
            ttsManager.onPlaybackStarted = null
            ttsManager.onPlaybackStopped = null
            ttsManager.onPlaybackCompleted = null
        }
    }



    // Mostrar diálogo de error de TTS si es necesario
    if (showTtsError) {
        AlertDialog(
            onDismissRequest = { showTtsError = false },
            title = { Text("Problema con la lectura por voz") },
            text = {
                Column {
                    Text(ttsErrorMessage)
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        "Esto puede deberse a que su dispositivo no tiene instaladas las voces necesarias o no es compatible con la función de lectura por voz.",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        // Intentar reiniciar el TTS
                        ttsManager.restart()
                        showTtsError = false
                    }
                ) {
                    Text("Reintentar")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showTtsError = false }
                ) {
                    Text("Cerrar")
                }
            }
        )
    }

    // Configuración del TTS Dialog
    if (showVoiceSettings) {
        // Asegurarse de que tengamos las voces disponibles
        LaunchedEffect(showVoiceSettings) {
            if (availableVoiceNames.isEmpty()) {
                // Si no hay voces cargadas, intentar cargarlas
                Log.d("MainActivity", "No hay voces cargadas, intentando cargar...")
                ttsManager.ensureInitialized()
                val voiceNames = ttsManager.getAvailableVoiceNames()
                availableVoiceNames.clear()
                availableVoiceNames.addAll(voiceNames)
                Log.d("MainActivity", "Voces cargadas en diálogo: ${voiceNames.size}")

                // Si aún no hay voz seleccionada, seleccionar la actual
                if (selectedVoiceName == null && voiceNames.isNotEmpty()) {
                    selectedVoiceName = ttsManager.getCurrentVoiceFriendlyName()
                    Log.d("MainActivity", "Seleccionando voz actual: $selectedVoiceName")
                }
            }
        }

        // Guardar los valores iniciales para detectar cambios
        val initialVoiceName = remember(showVoiceSettings) { selectedVoiceName }
        val initialSpeed = remember(showVoiceSettings) { currentSpeed }
        val initialPitch = remember(showVoiceSettings) { currentPitch }

        AlertDialog(
            onDismissRequest = {
                // Al hacer clic fuera del diálogo, simplemente lo cerramos sin aplicar cambios
                // Restauramos los valores iniciales
                selectedVoiceName?.let { ttsManager.setVoiceByFriendlyName(initialVoiceName ?: it) }
                currentSpeed = initialSpeed
                ttsManager.setSpeed(initialSpeed)
                currentPitch = initialPitch
                ttsManager.setPitch(initialPitch)

                // Cerramos el diálogo sin afectar la reproducción
                showVoiceSettings = false
            },
            title = { Text("Configuración de Voz") },
            text = {
                Column {
                    // Selector de voz (si hay disponibles)
                    if (availableVoiceNames.isNotEmpty()) {
                        Text(
                            text = "Voz:",
                            fontWeight = FontWeight.Medium,
                            fontSize = 16.sp,
                            modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
                        )

                        // Control del selector de voz y menú desplegable en un contenedor Box
                        Box(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            // Botón de selección de voz
                            OutlinedCard(
                                onClick = { expandedVoiceDropdown = true },
                                modifier = Modifier.fillMaxWidth(),
                                shape = RoundedCornerShape(8.dp),
                                border = BorderStroke(1.dp, MaterialTheme.colorScheme.primary.copy(alpha = 0.5f))
                            ) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = selectedVoiceName ?: "Voz predeterminada",
                                        fontSize = 16.sp,
                                        maxLines = 1,
                                        overflow = TextOverflow.Ellipsis,
                                        modifier = Modifier.weight(1f)
                                    )
                                    Icon(
                                        imageVector = Icons.Default.ArrowDropDown,
                                        contentDescription = "Seleccionar voz",
                                        tint = MaterialTheme.colorScheme.primary
                                    )
                                }
                            }

                            // Menú desplegable para las voces
                            DropdownMenu(
                                expanded = expandedVoiceDropdown,
                                onDismissRequest = { expandedVoiceDropdown = false },
                                modifier = Modifier
                                    .width(250.dp)
                                    .heightIn(max = 300.dp)
                                    .offset(x = (-20).dp)
                            ) {
                                availableVoiceNames.forEach { voiceName ->
                                    DropdownMenuItem(
                                        text = {
                                            Text(
                                                text = voiceName,
                                                maxLines = 1,
                                                overflow = TextOverflow.Ellipsis
                                            )
                                        },
                                        onClick = {
                                            selectedVoiceName = voiceName
                                            ttsManager.setVoiceByFriendlyName(voiceName)
                                            expandedVoiceDropdown = false
                                        },
                                        leadingIcon = {
                                            if (voiceName == selectedVoiceName) {
                                                Icon(
                                                    imageVector = Icons.Default.Check,
                                                    contentDescription = "Seleccionada",
                                                    tint = MaterialTheme.colorScheme.primary
                                                )
                                            }
                                        }
                                    )
                                }
                            }
                        }
                    }

                    // Control de velocidad
                    Text(
                        text = "Velocidad: ${String.format("%.1f", currentSpeed)}x",
                        fontWeight = FontWeight.Medium,
                        fontSize = 16.sp,
                        modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
                    )

                    Slider(
                        value = currentSpeed,
                        onValueChange = {
                            currentSpeed = it
                            ttsManager.setSpeed(it)
                            // No guardar en preferencias todavía, solo guardar al confirmar
                        },
                        valueRange = 0.5f..2.0f,
                        steps = 14, // 15 posiciones entre 0.5 y 2.0
                        modifier = Modifier.padding(vertical = 8.dp)
                    )

                    // Control de tono
                    Text(
                        text = "Tono: ${String.format("%.1f", currentPitch)}x",
                        fontWeight = FontWeight.Medium,
                        fontSize = 16.sp,
                        modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
                    )

                    Slider(
                        value = currentPitch,
                        onValueChange = {
                            currentPitch = it
                            ttsManager.setPitch(it)
                            // No guardar en preferencias todavía, solo guardar al confirmar
                        },
                        valueRange = 0.5f..2.0f,
                        steps = 14, // 15 posiciones entre 0.5 y 2.0
                        modifier = Modifier.padding(vertical = 8.dp)
                    )
                }
            },
            confirmButton = {
                TextButton(onClick = {
                    // Verificar si hubo cambios en los ajustes
                    val voiceChanged = selectedVoiceName != initialVoiceName
                    val speedChanged = currentSpeed != initialSpeed
                    val pitchChanged = currentPitch != initialPitch
                    val settingsChanged = voiceChanged || speedChanged || pitchChanged

                    Log.d("MainActivity", "Guardando cambios: voz=$voiceChanged, velocidad=$speedChanged, tono=$pitchChanged")

                    // Guardar la voz seleccionada
                    if (selectedVoiceName != null) {
                        setSelectedVoice(selectedVoiceName)
                        Log.d("MainActivity", "Voz guardada: $selectedVoiceName")
                    }

                    // Guardar velocidad y tono (siempre guardar para asegurar persistencia)
                    setSpeechRate(currentSpeed)
                    setSpeechPitch(currentPitch)
                    Log.d("MainActivity", "Velocidad y tono guardados: $currentSpeed, $currentPitch")

                    // Almacenamos si estaba reproduciendo para reanudar después
                    val wasPlaying = isPlaying

                    // Solo detener la reproducción si hubo cambios y está activa
                    if (settingsChanged && isPlaying) {
                        ttsManager.stop()
                    }

                    // Cerrar el diálogo
                    showVoiceSettings = false

                    // Reanudar la reproducción solo si hubo cambios y estaba activa
                    if (settingsChanged && wasPlaying) {
                        // Pequeña pausa para que los cambios se apliquen
                        val verses = BibleTestData.getVersesForChapter(selectedBook, selectedChapter)
                        ttsManager.speakVerses(verses, selectedVerse)
                    }
                }) {
                    Text("Guardar")
                }
            },
            dismissButton = {
                TextButton(onClick = {
                    // Restaurar los valores iniciales
                    selectedVoiceName?.let { ttsManager.setVoiceByFriendlyName(initialVoiceName ?: it) }
                    currentSpeed = initialSpeed
                    ttsManager.setSpeed(initialSpeed)
                    currentPitch = initialPitch
                    ttsManager.setPitch(initialPitch)

                    // Cerrar el diálogo sin afectar la reproducción
                    showVoiceSettings = false
                }) {
                    Text("Cancelar")
                }
            }
        )
    }





    // Efecto para abrir/cerrar el drawer cuando cambia showDrawer
    LaunchedEffect(showDrawer) {
        if (showDrawer) {
            drawerState.open()
        } else {
            drawerState.close()
        }
    }

    // Efecto para actualizar showDrawer cuando el drawer se cierra por gestos
    LaunchedEffect(drawerState.currentValue) {
        if (drawerState.currentValue == DrawerValue.Closed && showDrawer) {
            showDrawer = false
        }
    }

    ModalNavigationDrawer(
        drawerContent = {
            ModalDrawerSheet(
                modifier = Modifier.width(280.dp)
            ) {
                DrawerContent()
            }
        },
        drawerState = drawerState,
        gesturesEnabled = true
    ) {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = {
                        Text(
                            "OpenBiblia",
                            style = MaterialTheme.typography.titleLarge.copy(
                                fontWeight = FontWeight.Bold
                            )
                        )
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = MaterialTheme.colorScheme.surface,
                        titleContentColor = MaterialTheme.colorScheme.onSurface,
                    ),
                    modifier = Modifier.shadow(4.dp),
                    navigationIcon = {
                        IconButton(onClick = { showDrawer = true }) {
                            Icon(
                                imageVector = Icons.Outlined.Menu,
                                contentDescription = "Menú"
                            )
                        }
                    },
                actions = {
                    // Botón para cambiar entre tema oscuro y claro
                    IconButton(onClick = { onThemeChange(!isDarkTheme) }) {
                        Icon(
                            imageVector = if (isDarkTheme) {
                                Icons.Filled.LightMode
                            } else {
                                Icons.Filled.DarkMode
                            },
                            contentDescription = "Cambiar tema"
                        )
                    }
                }
            )
        },
        // Eliminamos el bottom navigation
        floatingActionButton = {
            // Contenedor para los botones flotantes
            Row(
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Botón de ajustes de voz (visible solo cuando se está reproduciendo)
                AnimatedVisibility(visible = isPlaying) {
                    FloatingActionButton(
                        onClick = {
                            if (!isTtsAvailable) {
                                // Mostrar mensaje de error si no hay voces disponibles
                                ttsErrorMessage = "No hay voces disponibles para configurar."
                                showTtsError = true
                                return@FloatingActionButton
                            }
                            showVoiceSettings = true
                        },
                        containerColor = if (isTtsAvailable)
                            MaterialTheme.colorScheme.secondaryContainer
                        else
                            MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.5f),
                        contentColor = MaterialTheme.colorScheme.onSecondaryContainer,
                        shape = CircleShape
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Settings,
                            contentDescription = "Ajustes de voz",
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }

                // Botón principal de reproducción
                FloatingActionButton(
                    onClick = {
                        if (!isTtsAvailable && !isPlaying) {
                            // Mostrar mensaje de error si no hay voces disponibles
                            ttsErrorMessage = "No hay voces disponibles para reproducir el texto."
                            showTtsError = true
                            return@FloatingActionButton
                        }

                        val verses = BibleTestData.getVersesForChapter(selectedBook, selectedChapter)
                        if (isPlaying) {
                            ttsManager.stop()
                        } else {
                            ttsManager.speakVerses(verses, selectedVerse)
                        }
                    },
                    containerColor = if (isTtsAvailable || isPlaying)
                        MaterialTheme.colorScheme.primary
                    else
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.5f),
                    contentColor = MaterialTheme.colorScheme.onPrimary,
                    shape = CircleShape,
                    // No deshabilitamos el botón completamente para poder mostrar el mensaje de error
                ) {
                    Icon(
                        imageVector = if (isPlaying) Icons.Filled.Stop else Icons.Filled.PlayArrow,
                        contentDescription = if (isPlaying) "Detener" else "Reproducir capítulo",
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
        }
    ) { paddingValues ->

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp, vertical = 8.dp)
        ) {
            // Selectores de Libro, Capítulo y Versículo
            SelectorRow(
                selectedBook = selectedBook,
                selectedChapter = selectedChapter,
                selectedVerse = selectedVerse,
                isDarkTheme = isDarkTheme,
                onBookSelected = { newBook ->
                    // Al cambiar de libro, reiniciamos capítulo y versículo a 1
                    onBookSelected(newBook)
                    onChapterSelected("1")
                    onVerseSelected("1")

                    // Guardar la nueva posición de lectura
                    try {
                        preferencesManager.javaClass.getMethod("saveLastReadingPosition", String::class.java, String::class.java, String::class.java)
                            .invoke(preferencesManager, newBook, "1", "1")
                    } catch (e: Exception) {
                        // No hacer nada si el método no existe
                    }

                    // No activamos el resaltado al cambiar de libro
                    verseExplicitlySelected = false
                    // Detener la reproducción al cambiar de libro
                    ttsManager.stop()
                },
                onChapterSelected = {
                    onChapterSelected(it)
                    // Al cambiar de capítulo manualmente, mantenemos la selección de versículo
                    // pero reiniciamos el versículo a 1 para evitar problemas
                    onVerseSelected("1")

                    // Guardar la nueva posición de lectura
                    try {
                        preferencesManager.javaClass.getMethod("saveLastReadingPosition", String::class.java, String::class.java, String::class.java)
                            .invoke(preferencesManager, selectedBook, it, "1")
                    } catch (e: Exception) {
                        // No hacer nada si el método no existe
                    }

                    verseExplicitlySelected = false
                    // Detener la reproducción al cambiar de capítulo
                    ttsManager.stop()
                },
                onVerseSelected = {
                    onVerseSelected(it)

                    // Guardar la nueva posición de lectura
                    try {
                        preferencesManager.javaClass.getMethod("saveLastReadingPosition", String::class.java, String::class.java, String::class.java)
                            .invoke(preferencesManager, selectedBook, selectedChapter, it)
                    } catch (e: Exception) {
                        // No hacer nada si el método no existe
                    }

                    verseExplicitlySelected = true
                },
                onPreviousChapter = {
                    val chapters = BibleTestData.getChapterNumbers(selectedBook)
                    val currentIndex = chapters.indexOf(selectedChapter)

                    // Guardar nueva posición
                    if (currentIndex > 0) {
                        val newChapter = chapters[currentIndex - 1]
                        onChapterSelected(newChapter)

                        // Guardar la nueva posición de lectura
                        try {
                            preferencesManager.javaClass.getMethod("saveLastReadingPosition", String::class.java, String::class.java, String::class.java)
                                .invoke(preferencesManager, selectedBook, newChapter, "1")
                        } catch (e: Exception) {
                            // No hacer nada si el método no existe
                        }
                    } else {
                        // Si estamos en el primer capítulo, ir al libro anterior
                        val books = BibleTestData.getBookNames()
                        val currentBookIndex = books.indexOf(selectedBook)
                        if (currentBookIndex > 0) {
                            val previousBook = books[currentBookIndex - 1]
                            onBookSelected(previousBook)

                            // Seleccionar el último capítulo del libro anterior
                            val previousBookChapters = BibleTestData.getChapterNumbers(previousBook)
                            val lastChapter = previousBookChapters.last()
                            onChapterSelected(lastChapter)

                            // Guardar la nueva posición de lectura
                            try {
                                preferencesManager.javaClass.getMethod("saveLastReadingPosition", String::class.java, String::class.java, String::class.java)
                                    .invoke(preferencesManager, previousBook, lastChapter, "1")
                            } catch (e: Exception) {
                                // No hacer nada si el método no existe
                            }
                        }
                    }
                    // Al navegar entre capítulos, reiniciamos a versículo 1 y desactivamos el resaltado
                    onVerseSelected("1")
                    verseExplicitlySelected = false
                    // Detener la reproducción al cambiar de capítulo
                    ttsManager.stop()
                },
                onNextChapter = {
                    val chapters = BibleTestData.getChapterNumbers(selectedBook)
                    val currentIndex = chapters.indexOf(selectedChapter)

                    // Guardar nueva posición
                    if (currentIndex < chapters.size - 1) {
                        val newChapter = chapters[currentIndex + 1]
                        onChapterSelected(newChapter)

                        // Guardar la nueva posición de lectura
                        try {
                            preferencesManager.javaClass.getMethod("saveLastReadingPosition", String::class.java, String::class.java, String::class.java)
                                .invoke(preferencesManager, selectedBook, newChapter, "1")
                        } catch (e: Exception) {
                            // No hacer nada si el método no existe
                        }
                    } else {
                        // Si estamos en el último capítulo, ir al libro siguiente
                        val books = BibleTestData.getBookNames()
                        val currentBookIndex = books.indexOf(selectedBook)
                        if (currentBookIndex < books.size - 1) {
                            val nextBook = books[currentBookIndex + 1]
                            onBookSelected(nextBook)

                            // Seleccionar el primer capítulo del libro siguiente
                            onChapterSelected("1")

                            // Guardar la nueva posición de lectura
                            try {
                                preferencesManager.javaClass.getMethod("saveLastReadingPosition", String::class.java, String::class.java, String::class.java)
                                    .invoke(preferencesManager, nextBook, "1", "1")
                            } catch (e: Exception) {
                                // No hacer nada si el método no existe
                            }
                        }
                    }
                    // Al navegar entre capítulos, reiniciamos a versículo 1 y desactivamos el resaltado
                    onVerseSelected("1")
                    verseExplicitlySelected = false
                    // Detener la reproducción al cambiar de capítulo
                    ttsManager.stop()
                }
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Contenido de la Biblia
            BibleContent(
                book = selectedBook,
                chapter = selectedChapter,
                selectedVerse = selectedVerse,
                showVerseHighlight = verseExplicitlySelected,
                isInitialLaunch = isInitialLaunch,
                onInitialScrollComplete = onInitialScrollComplete,
                ttsManager = ttsManager,
                showAudioControls = false, // Ya no mostramos los controles de audio dentro del contenido
                preferencesManager = preferencesManager as PreferencesManager, // Pasamos el PreferencesManager
                isDarkTheme = isDarkTheme // Pasamos el estado del tema
            )
        }
    }
    }
}

@Composable
fun DrawerMenuItem(icon: androidx.compose.ui.graphics.vector.ImageVector, text: String, onClick: () -> Unit = {}) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(vertical = 12.dp, horizontal = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary
        )
        Text(
            text = text,
            style = MaterialTheme.typography.bodyLarge
        )
    }
}

@Composable
fun SelectorRow(
    selectedBook: String,
    selectedChapter: String,
    selectedVerse: String,
    onBookSelected: (String) -> Unit,
    onChapterSelected: (String) -> Unit,
    onVerseSelected: (String) -> Unit,
    onPreviousChapter: () -> Unit,
    onNextChapter: () -> Unit,
    isDarkTheme: Boolean = false
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .shadow(
                elevation = 4.dp,
                shape = RoundedCornerShape(16.dp)
            ),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isDarkTheme) Color(0xFF2B2B2B) else Color(0xFFEBE0B4)
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // Primera fila: Selectores
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // Selector de Libro
                SelectorButton(
                    title = "Libro",
                    selected = selectedBook,
                    options = BibleTestData.getBookNames(),
                    onSelected = onBookSelected,
                    modifier = Modifier.weight(1.6f),
                    icon = Icons.Default.Book,
                    maxTextLength = 10 // Truncamos nombres largos de libros
                )

                Spacer(modifier = Modifier.width(8.dp))

                // Selector de Capítulo
                SelectorButton(
                    title = "Capítulo",
                    selected = selectedChapter,
                    options = BibleTestData.getChapterNumbers(selectedBook),
                    onSelected = onChapterSelected,
                    modifier = Modifier.weight(0.7f),
                    icon = Icons.Default.Numbers,
                    maxTextLength = 3, // Números de capítulo cortos, sin truncar
                    isNumber = true
                )

                Spacer(modifier = Modifier.width(8.dp))

                // Selector de Versículo
                SelectorButton(
                    title = "Versículo",
                    selected = selectedVerse,
                    options = BibleTestData.getVerseNumbers(selectedBook, selectedChapter),
                    onSelected = onVerseSelected,
                    modifier = Modifier.weight(0.7f),
                    icon = Icons.Default.TextFields,
                    maxTextLength = 3, // Números de versículo cortos, sin truncar
                    isNumber = true
                )
            }

            // Pequeño espacio entre las filas (sin línea divisora)
            Spacer(modifier = Modifier.height(8.dp))

            // Segunda fila: Botones de navegación más compactos
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Button(
                    onClick = onPreviousChapter,
                    modifier = Modifier.weight(1f),
                    contentPadding = PaddingValues(vertical = 4.dp, horizontal = 12.dp),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Capítulo anterior",
                        modifier = Modifier.size(14.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        "Anterior",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium
                    )
                }

                Spacer(modifier = Modifier.width(12.dp))

                Button(
                    onClick = onNextChapter,
                    modifier = Modifier.weight(1f),
                    contentPadding = PaddingValues(vertical = 4.dp, horizontal = 12.dp),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        "Siguiente",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Icon(
                        imageVector = Icons.Default.ArrowForward,
                        contentDescription = "Capítulo siguiente",
                        modifier = Modifier.size(14.dp)
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SelectorButton(
    title: String,
    selected: String,
    options: List<String>,
    onSelected: (String) -> Unit,
    modifier: Modifier = Modifier,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    maxTextLength: Int = Int.MAX_VALUE,
    isNumber: Boolean = false
) {
    var expanded by remember { mutableStateOf(false) }

    Column(modifier = modifier) {
        Text(
            text = title,
            style = MaterialTheme.typography.labelMedium,
            modifier = Modifier.padding(bottom = 4.dp)
        )

        OutlinedCard(
            modifier = Modifier
                .fillMaxWidth()
                .defaultMinSize(minWidth = if (isNumber) 60.dp else 80.dp),
            onClick = { expanded = true },
            border = BorderStroke(1.dp, MaterialTheme.colorScheme.primary.copy(alpha = 0.5f)),
            shape = RoundedCornerShape(8.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp, horizontal = if (isNumber) 6.dp else 12.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = if (isNumber) Arrangement.Center else Arrangement.SpaceBetween
            ) {
                if (!isNumber) {
                    Icon(
                        imageVector = icon,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(16.dp)
                    )
                }

                // Para números, usamos un diseño centrado sin truncado
                if (isNumber) {
                    Text(
                        text = selected,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(horizontal = 2.dp),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                } else {
                    // Para textos como nombres de libros, permitimos truncado
                    Text(
                        text = selected,
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .weight(1f)
                            .padding(horizontal = 4.dp),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        fontSize = 14.sp
                    )
                }

                if (!isNumber) {
                    Icon(
                        imageVector = Icons.Default.ArrowDropDown,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }

        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false },
            modifier = Modifier.heightIn(max = 300.dp)
        ) {
            options.forEach { option ->
                val truncatedText = if (option.length > maxTextLength)
                    option.take(maxTextLength - 3) + "..."
                else
                    option

                DropdownMenuItem(
                    text = {
                        Text(
                            text = option,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    },
                    onClick = {
                        onSelected(option)
                        expanded = false
                    },
                    leadingIcon = {
                        if (option == selected) {
                            Icon(
                                imageVector = Icons.Default.Check,
                                contentDescription = "Seleccionado",
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                )
            }
        }
    }
}

@Composable
fun BibleContent(
    book: String,
    chapter: String,
    selectedVerse: String,
    showVerseHighlight: Boolean,
    isInitialLaunch: Boolean,
    onInitialScrollComplete: () -> Unit,
    ttsManager: TtsManager,
    showAudioControls: Boolean,
    preferencesManager: PreferencesManager = PreferencesManager(LocalContext.current), // Agregamos el PreferencesManager con un valor por defecto
    isDarkTheme: Boolean = false // Agregamos el parámetro para el tema
) {
    val verses = BibleTestData.getVersesForChapter(book, chapter)

    // Estado para controlar TTS a nivel de BibleContent
    var isPlaying by remember { mutableStateOf(false) }
    var currentSpeed by remember { mutableStateOf(1.0f) }
    var currentPitch by remember { mutableStateOf(1.0f) }

    // Estado para el indicador visual de procesamiento
    val isProcessing by ttsManager.isProcessingFlow.collectAsState(initial = false)

    // Estado para el versículo actual que está siendo leído
    var currentReadingVerse by remember { mutableStateOf<String?>(null) }

    // Actualizar estados cada vez que cambia el capítulo o versículo
    LaunchedEffect(book, chapter) {
        // Detener la reproducción al cambiar de capítulo o libro
        ttsManager.stop()
        isPlaying = false
        currentReadingVerse = null
    }

    // Recolectar el flujo de estado para el versículo actual
    LaunchedEffect(Unit) {
        ttsManager.currentVerseFlow.collect { verse ->
            currentReadingVerse = verse
        }
    }

    // Observar el estado de reproducción del TTS
    DisposableEffect(Unit) {
        val originalOnPlaybackStarted = ttsManager.onPlaybackStarted
        val originalOnPlaybackStopped = ttsManager.onPlaybackStopped
        val originalOnPlaybackCompleted = ttsManager.onPlaybackCompleted
        val originalOnVerseChanged = ttsManager.onVerseChanged

        ttsManager.onPlaybackStarted = {
            isPlaying = true
            originalOnPlaybackStarted?.invoke()
        }

        ttsManager.onPlaybackStopped = {
            isPlaying = false
            currentReadingVerse = null
            originalOnPlaybackStopped?.invoke()
        }

        ttsManager.onPlaybackCompleted = {
            isPlaying = false
            currentReadingVerse = null
            originalOnPlaybackCompleted?.invoke()
        }

        ttsManager.onVerseChanged = { verse ->
            currentReadingVerse = verse
            originalOnVerseChanged?.invoke(verse)
        }

        onDispose {
            ttsManager.onPlaybackStarted = originalOnPlaybackStarted
            ttsManager.onPlaybackStopped = originalOnPlaybackStopped
            ttsManager.onPlaybackCompleted = originalOnPlaybackCompleted
            ttsManager.onVerseChanged = originalOnVerseChanged
        }
    }

    // ScrollState controlado para poder desplazarnos programáticamente
    val scrollState = rememberScrollState()

    // Referencias a las posiciones de los versículos
    val versePositions = remember { mutableStateMapOf<String, Int>() }

    // Creamos una clave única para el capítulo actual
    val chapterKey = "$book-$chapter"

    // Efecto para desplazarse al inicio al cambiar de capítulo
    LaunchedEffect(chapterKey) {
        // Solo realizamos el desplazamiento al inicio si no es el lanzamiento inicial
        // y no estamos volviendo de ajustes (donde isInitialLaunch será true)
        if (!isInitialLaunch && !showVerseHighlight) {
            // Desplazamiento suave al inicio del capítulo
            scrollState.animateScrollTo(0, animationSpec = tween(500, easing = FastOutSlowInEasing))
        }
    }

    // Efecto para desplazarse al versículo seleccionado explícitamente
    LaunchedEffect(selectedVerse, showVerseHighlight) {
        if (verses.containsKey(selectedVerse)) {
            // Esperamos a que las posiciones de los versículos estén disponibles
            // Esto es importante cuando volvemos de ajustes
            var attempts = 0
            while (!versePositions.containsKey(selectedVerse) && attempts < 20) {
                delay(25) // Espera más corta para ser más responsivo
                attempts++
            }

            versePositions[selectedVerse]?.let { position ->
                // Usamos una animación más suave y consistente
                scrollState.animateScrollTo(
                    position,
                    animationSpec = tween(
                        durationMillis = 500,
                        easing = LinearOutSlowInEasing // Curva de aceleración más suave
                    )
                )
            }
        }
    }

    // Efecto para el desplazamiento inicial cuando se abre la app o se vuelve de ajustes
    LaunchedEffect(isInitialLaunch) {
        if (isInitialLaunch && verses.containsKey(selectedVerse)) {
            // Esperamos a que las posiciones de los versículos estén disponibles
            var attempts = 0
            while (!versePositions.containsKey(selectedVerse) && attempts < 20) {
                delay(25) // Espera más corta para ser más responsivo
                attempts++
            }

            versePositions[selectedVerse]?.let { position ->
                // Usamos la misma especificación de animación que al seleccionar un versículo
                // para mantener la consistencia visual
                scrollState.animateScrollTo(
                    position,
                    animationSpec = tween(
                        durationMillis = 500, // Misma duración que al seleccionar un versículo
                        easing = LinearOutSlowInEasing // Curva de aceleración más suave
                    )
                )
            }
            // Notificar que el desplazamiento inicial está completo
            onInitialScrollComplete()
        }
    }

    // Efecto para desplazarse al versículo que se está leyendo actualmente
    LaunchedEffect(currentReadingVerse) {
        if (currentReadingVerse != null && verses.containsKey(currentReadingVerse)) {
            versePositions[currentReadingVerse]?.let { position ->
                // Desplazamiento suave al versículo actual con la misma animación que los demás
                scrollState.animateScrollTo(
                    position,
                    animationSpec = tween(
                        durationMillis = 500,
                        easing = LinearOutSlowInEasing
                    )
                )
            }
        }
    }

    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        // Contenido de la Biblia
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .shadow(
                    elevation = 6.dp,
                    shape = RoundedCornerShape(16.dp),
                    spotColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                ),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Column(
                modifier = Modifier
                    .padding(20.dp)
                    .verticalScroll(scrollState)
            ) {
                // Título decorativo
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp)
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        // Icono decorativo
                        Surface(
                            modifier = Modifier
                                .size(48.dp)
                                .shadow(4.dp, CircleShape),
                            shape = CircleShape,
                            color = MaterialTheme.colorScheme.primary
                        ) {
                            Icon(
                                imageVector = Icons.Default.MenuBook,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.onPrimary,
                                modifier = Modifier
                                    .size(24.dp)
                                    .padding(12.dp)
                            )
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        // Título del capítulo
                        Text(
                            text = "$book",
                            style = MaterialTheme.typography.headlineSmall.copy(
                                fontWeight = FontWeight.Bold,
                                fontFamily = preferencesManager.getFontFamily()
                            ),
                            textAlign = TextAlign.Center
                        )

                        Text(
                            text = "Capítulo $chapter",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontStyle = FontStyle.Italic,
                                color = MaterialTheme.colorScheme.primary,
                                fontFamily = preferencesManager.getFontFamily()
                            ),
                            textAlign = TextAlign.Center
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        // Línea decorativa
                        Box(
                            modifier = Modifier
                                .height(2.dp)
                                .width(120.dp)
                                .background(
                                    brush = Brush.horizontalGradient(
                                        colors = listOf(
                                            Color.Transparent,
                                            MaterialTheme.colorScheme.primary,
                                            Color.Transparent
                                        )
                                    )
                                )
                        )
                    }
                }

                // Mostramos mensaje si no hay versículos disponibles
                if (verses.isEmpty()) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 32.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "No hay versículos disponibles para este capítulo",
                            style = MaterialTheme.typography.bodyLarge.copy(
                                fontStyle = FontStyle.Italic,
                                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                            ),
                            textAlign = TextAlign.Center
                        )
                    }
                }

                // Versículos
                verses.toSortedMap { a, b -> a.toInt().compareTo(b.toInt()) }.forEach { (number, text) ->
                    // Esta key es para identificar cada versículo
                    key(book, chapter, number) {
                        // Determinar qué versículo debe resaltarse
                        val isExplicitlySelected = number == selectedVerse && showVerseHighlight
                        val isCurrentlyReading = number == currentReadingVerse && isPlaying

                        // Definir el tipo de resaltado
                        val highlightType = when {
                            isCurrentlyReading -> HighlightType.Reading
                            isExplicitlySelected -> HighlightType.Selected
                            else -> HighlightType.None
                        }

                        // Animación para versículo seleccionado
                        val infiniteTransition = rememberInfiniteTransition()
                        val pulseAlpha by infiniteTransition.animateFloat(
                            initialValue = 0.1f,
                            targetValue = 0.2f,
                            animationSpec = infiniteRepeatable(
                                animation = tween(1000),
                                repeatMode = RepeatMode.Reverse
                            )
                        )

                        // Color de fondo según tipo de resaltado
                        val backgroundColor by animateColorAsState(
                            targetValue = when (highlightType) {
                                HighlightType.Reading -> MaterialTheme.colorScheme.secondary.copy(alpha = 0.15f)
                                HighlightType.Selected -> MaterialTheme.colorScheme.primary.copy(alpha = pulseAlpha)
                                HighlightType.None -> Color.Transparent
                            },
                            animationSpec = tween(durationMillis = 300)
                        )

                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 12.dp)
                                .background(
                                    color = backgroundColor,
                                    shape = RoundedCornerShape(8.dp)
                                )
                                // Guardamos la posición del versículo para poder desplazarnos a él
                                .onGloballyPositioned { coordinates ->
                                    versePositions[number] = coordinates.positionInParent().y.toInt()
                                }
                                .padding(8.dp)
                                // Hacemos que el versículo sea clicable para seleccionarlo y reproducirlo
                                .clickable {
                                    // Verificar si TTS está disponible
                                    val isTtsAvailable = ttsManager.areVoicesAvailable()

                                    // Siempre enfocamos el versículo seleccionado
                                    // Esto se hace a través del estado de currentReadingVerse
                                    // que ya está conectado con los efectos de desplazamiento

                                    if (isTtsAvailable) {
                                        // Solo reproducimos si TTS está disponible
                                        ttsManager.speakVerse(text, number)
                                    } else {
                                        // Si TTS no está disponible, solo actualizamos el estado
                                        // para enfocar el versículo sin reproducirlo
                                        currentReadingVerse = number

                                        // Mostrar un mensaje de error (opcional)
                                        // Esto se podría implementar con un SnackbarHostState
                                        // pero por ahora lo dejamos así para no complicar el código
                                    }
                                }
                        ) {
                            Row(
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                // Número de versículo
                                Surface(
                                    shape = CircleShape,
                                    color = when (highlightType) {
                                        HighlightType.Reading -> MaterialTheme.colorScheme.secondary.copy(alpha = 0.3f)
                                        HighlightType.Selected -> MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
                                        HighlightType.None -> MaterialTheme.colorScheme.primary.copy(alpha = 0.15f)
                                    },
                                    modifier = Modifier.padding(top = 3.dp, end = 8.dp)
                                ) {
                                    Text(
                                        text = "$number",
                                        fontWeight = FontWeight.Bold,
                                        modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp),
                                        fontSize = 12.sp,
                                        color = when {
                                            // Si es modo claro y no está siendo leído, usar color negro
                                            !isDarkTheme && highlightType != HighlightType.Reading -> Color.Black
                                            // Si está siendo leído, usar el color secundario (naranja)
                                            highlightType == HighlightType.Reading -> MaterialTheme.colorScheme.secondary
                                            // En otros casos (modo oscuro), usar el color primario (dorado/ámbar)
                                            else -> MaterialTheme.colorScheme.primary
                                        },
                                        textAlign = TextAlign.Center
                                    )
                                }

                                // Texto del versículo
                                Text(
                                    text = text,
                                    style = MaterialTheme.typography.bodyLarge.copy(
                                        fontSize = preferencesManager.getTextSize().sp,
                                        fontFamily = preferencesManager.getFontFamily(),
                                        lineHeight = (preferencesManager.getTextSize() * 1.3).sp, // Ajustamos el interlineado según el tamaño de texto
                                        textIndent = TextIndent(firstLine = 0.sp),
                                        fontWeight = when (highlightType) {
                                            HighlightType.Reading -> FontWeight.SemiBold
                                            HighlightType.Selected -> FontWeight.Medium
                                            HighlightType.None -> FontWeight.Normal
                                        }
                                    )
                                )
                            }
                        }
                    }
                }
            }
        }

        // Añadir espacio entre el contenido y los controles
        Spacer(modifier = Modifier.height(16.dp))

        // Controles de audio
        if (showAudioControls) {
            // Verificar si TTS está disponible
            val isTtsAvailable = ttsManager.areVoicesAvailable()

            AudioControls(
                isPlaying = isPlaying,
                currentSpeed = currentSpeed,
                currentPitch = currentPitch,
                onPlayPauseClick = {
                    if (!isTtsAvailable && !isPlaying) {
                        // Si TTS no está disponible, no hacer nada
                        // Aquí se podría mostrar un mensaje de error
                        return@AudioControls
                    }

                    if (isPlaying) {
                        ttsManager.stop()
                    } else {
                        // Reproducir desde el versículo seleccionado
                        ttsManager.speakVerses(verses, selectedVerse)
                    }
                },
                onStopClick = {
                    ttsManager.stop()
                },
                onSpeedChanged = { newSpeed ->
                    currentSpeed = newSpeed
                    ttsManager.setSpeed(newSpeed)
                },
                onPitchChanged = { newPitch ->
                    currentPitch = newPitch
                    ttsManager.setPitch(newPitch)
                },
                // Deshabilitar controles si TTS no está disponible
                enabled = isTtsAvailable || isPlaying,
                // Pasar el estado de procesamiento para el indicador visual
                isProcessing = isProcessing
            )
        }
    }
}

// Enum para los diferentes tipos de resaltado
enum class HighlightType {
    None,
    Selected,
    Reading
}

@Preview(showBackground = true)
@Composable
fun MainScreenPreview() {
    // Para la vista previa, usamos un TtsManager simulado
    class MockTtsManager : TtsManager(null) {
        override fun speakVerse(text: String, verseNumber: String) {}
        override fun speakVerses(verses: Map<String, String>, startVerseNumber: String) {}
        override fun stop() {}
        override fun setSpeed(speed: Float) {}
        override fun setPitch(pitch: Float) {}
        override fun isPlaying(): Boolean = false
        override fun getCurrentSpeed(): Float = 1.0f
        override fun getCurrentPitch(): Float = 1.0f
        override fun getAvailableVoices(): List<android.speech.tts.Voice> = emptyList()
        override fun getCurrentVoice(): android.speech.tts.Voice? = null
        override fun setVoice(voice: android.speech.tts.Voice) {}
        override fun getVoiceNameMap(): Map<String, android.speech.tts.Voice> = mapOf(
            "Voz 1 (es)" to android.speech.tts.Voice("es-es-x-eef-local", Locale("es"), 500, 500, false, setOf()),
            "Voz 2 (es)" to android.speech.tts.Voice("es-us-x-esd-local", Locale("es"), 300, 400, false, setOf()),
            "Voz 3 (es)" to android.speech.tts.Voice("es-us-x-esf-local", Locale("es"), 500, 500, false, setOf()),
            "Voz 4 (es)" to android.speech.tts.Voice("es-es-x-eec-local", Locale("es"), 500, 500, false, setOf()),
            "Voz 5 (es)" to android.speech.tts.Voice("es-us-language", Locale("es"), 400, 300, false, setOf())
        )
        override fun getCurrentVoiceFriendlyName(): String = "Voz 1 (es)"
        override fun getAvailableVoiceNames(): List<String> = listOf("Voz 1 (es)", "Voz 2 (es)", "Voz 3 (es)", "Voz 4 (es)", "Voz 5 (es)")
        override fun setVoiceByFriendlyName(friendlyName: String) {}
        override fun shutdown() {}
    }

    // Crear un PreferencesManager simulado para la vista previa sin extender de PreferencesManager
    class MockPreferencesManager {
        fun isDarkTheme(): Boolean = true
        fun getSpeechRate(): Float = 1.0f
        fun getSpeechPitch(): Float = 1.0f
        fun getSelectedVoice(): String? = "Voz 1 (es)"
        fun setDarkTheme(_isDarkTheme: Boolean) {}
        fun setSpeechRate(_rate: Float) {}
        fun setSpeechPitch(_pitch: Float) {}
        fun setSelectedVoice(_voiceName: String?) {}
        fun getLastBook(): String = "Génesis"
        fun getLastChapter(): String = "1"
        fun getLastVerse(): String = "1"
        fun saveLastReadingPosition(_book: String, _chapter: String, _verse: String) {}
        fun getTextSize(): Int = 18
        fun setTextSize(_size: Int) {}
        fun getFontFamily(): FontFamily = FontFamily.SansSerif
        fun setFontFamily(_fontFamily: FontFamily) {}
        fun getHighlightColor(): Color = Color(0xFFFFEB3B)
        fun setHighlightColor(_color: Color) {}
    }

    MyBibleTheme(darkTheme = true) {
        MainScreen(
            isDarkTheme = true,
            onThemeChange = {},
            selectedBook = "Génesis",
            selectedChapter = "1",
            selectedVerse = "3",
            isInitialLaunch = true,
            onInitialScrollComplete = {},
            onBookSelected = {},
            onChapterSelected = {},
            onVerseSelected = {},
            onNavigateToSettings = {},
            ttsManager = MockTtsManager(),
            preferencesManager = MockPreferencesManager()
        )
    }
}

@Preview(showBackground = true)
@Composable
fun BibleContentPreview() {
    // Para la vista previa, usamos un TtsManager simulado
    class MockTtsManager : TtsManager(null) {
        override fun speakVerse(text: String, verseNumber: String) {}
        override fun speakVerses(verses: Map<String, String>, startVerseNumber: String) {}
        override fun stop() {}
        override fun setSpeed(speed: Float) {}
        override fun setPitch(pitch: Float) {}
        override fun isPlaying(): Boolean = false
        override fun getCurrentSpeed(): Float = 1.0f
        override fun getCurrentPitch(): Float = 1.0f
        override fun getAvailableVoices(): List<android.speech.tts.Voice> = emptyList()
        override fun getCurrentVoice(): android.speech.tts.Voice? = null
        override fun setVoice(voice: android.speech.tts.Voice) {}
        override fun getVoiceNameMap(): Map<String, android.speech.tts.Voice> = mapOf()
        override fun getCurrentVoiceFriendlyName(): String = "Voz 1 (es)"
        override fun getAvailableVoiceNames(): List<String> = listOf("Voz 1 (es)")
        override fun setVoiceByFriendlyName(friendlyName: String) {}
        override fun shutdown() {}
    }

    // Crear un PreferencesManager simulado para la vista previa
    class MockPreferencesManager : PreferencesManager(null) {
        override fun getTextSize(): Int = 18
        override fun getFontFamily(): FontFamily = FontFamily.SansSerif
        // Sobrescribimos los métodos que dependen de SharedPreferences
        override fun setTextSize(size: Int) {}
        override fun setFontFamily(fontFamily: FontFamily) {}
        override fun isDarkTheme(): Boolean = true
        override fun setDarkTheme(isDarkTheme: Boolean) {}
    }

    MyBibleTheme(darkTheme = true) {
        BibleContent(
            book = "Génesis",
            chapter = "1",
            selectedVerse = "1",
            showVerseHighlight = false,
            isInitialLaunch = false,
            onInitialScrollComplete = {},
            ttsManager = MockTtsManager(),
            showAudioControls = false,
            preferencesManager = MockPreferencesManager(),
            isDarkTheme = true
        )
    }
}