package com.develop.mybible.ui.splash

import android.content.Context
import android.os.Build
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.develop.mybible.PreferencesManager
import com.develop.mybible.R
import com.develop.mybible.data.BibleTestData
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Función para precargar datos esenciales de la Biblia en segundo plano
 */
private suspend fun preloadBibleData(context: Context) {
    withContext(Dispatchers.IO) {
        try {
            // Inicializar el repositorio si es necesario
            BibleTestData.initialize(context)

            // Precargar datos básicos
            val books = BibleTestData.getBookNames()
            if (books.isNotEmpty()) {
                // Precargar capítulos del primer libro
                val firstBook = books.first()
                val chapters = BibleTestData.getChapterNumbers(firstBook)

                if (chapters.isNotEmpty()) {
                    // Precargar versículos del primer capítulo
                    val firstChapter = chapters.first()
                    BibleTestData.getVersesForChapter(firstBook, firstChapter)
                }
            }
        } catch (e: Exception) {
            // Manejar cualquier error silenciosamente para no interrumpir el splash
        }
    }
}

@Composable
fun SplashScreen(
    onSplashFinished: () -> Unit
) {
    // Obtener el contexto actual
    val context = LocalContext.current

    // Obtener el tema actual desde PreferencesManager
    val preferencesManager = remember { PreferencesManager(context) }
    val isDarkTheme = remember { preferencesManager.isDarkTheme() }

    // Estado para controlar la visibilidad del contenido
    var isVisible by remember { mutableStateOf(false) }
    var isLogoVisible by remember { mutableStateOf(false) }
    var isDataLoaded by remember { mutableStateOf(false) }

    // Usar colores del tema actual
    val backgroundColor = if (isDarkTheme) {
        MaterialTheme.colorScheme.background
    } else {
        MaterialTheme.colorScheme.surface // Usar color blanco/superficie para el tema claro
    }

    // Determinar si estamos en Android 12 o superior
    val isAndroid12OrHigher = Build.VERSION.SDK_INT >= Build.VERSION_CODES.S

    // Seleccionar la imagen del logo según el tema (solo se usará en versiones anteriores a Android 12)
    val logoResourceId = if (isDarkTheme) {
        R.drawable.icon_splash // Logo normal para tema oscuro
    } else {
        R.drawable.icon_splash_white // Logo invertido para tema claro
    }

    // Color del texto para "OpenBiblia" (se usará en Android 12 o superior)
    val appNameTextColor = if (isDarkTheme) {
        MaterialTheme.colorScheme.primary // Color dorado/ámbar en tema oscuro
    } else {
        MaterialTheme.colorScheme.onSurface // Color negro en tema claro
    }

    // Efecto para precargar datos y manejar la animación
    LaunchedEffect(key1 = true) {
        // Iniciar la secuencia de animación
        isVisible = true

        // Lanzar la precarga de datos en segundo plano
        val dataLoadingJob = CoroutineScope(Dispatchers.Main).launch {
            preloadBibleData(context)
            isDataLoaded = true
        }

        // Continuar con la animación mientras se cargan los datos
        delay(100) // Pequeña pausa antes de mostrar el logo
        isLogoVisible = true

        // Esperar al menos 1500ms para que se vea la animación completa
        delay(1800)

        // Esperar a que los datos terminen de cargar si aún no han terminado
        dataLoadingJob.join()

        // Navegar a la siguiente pantalla
        onSplashFinished()
    }

    // Contenedor principal con el fondo de color
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor),
        contentAlignment = Alignment.Center
    ) {
        // Box para desplazar el contenido hacia arriba
        Box(
            modifier = Modifier
                .offset(y = (-40).dp), // Desplazamiento fijo hacia arriba (15% aproximadamente)
            contentAlignment = Alignment.Center
        ) {
            // Contenido condicional basado en la versión de Android
            AnimatedVisibility(
                visible = isLogoVisible,
                enter = fadeIn(
                    animationSpec = tween(
                        durationMillis = 500,
                        easing = FastOutSlowInEasing
                    )
                ) + slideInVertically(
                    initialOffsetY = { -50 },
                    animationSpec = tween(
                        durationMillis = 500,
                        easing = FastOutSlowInEasing
                    )
                )
            ) {
                if (isAndroid12OrHigher) {
                    // Para Android 12 o superior, mostrar texto "OpenBiblia"
                    Text(
                        text = "OpenBiblia",
                        color = appNameTextColor,
                        fontSize = 40.sp,
                        fontWeight = FontWeight.Bold,
                        fontFamily = androidx.compose.ui.text.font.FontFamily.SansSerif,
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .fillMaxWidth(0.8f)
                    )
                } else {
                    // Para versiones anteriores, mostrar el logo
                    Image(
                        painter = painterResource(id = logoResourceId),
                        contentDescription = "Logo Mi Biblia",
                        modifier = Modifier
                            .fillMaxWidth(0.6f) // Ocupa el 60% del ancho disponible
                            .aspectRatio(1f) // Mantiene la relación de aspecto 1:1
                    )
                }
            }
        }
    }
}