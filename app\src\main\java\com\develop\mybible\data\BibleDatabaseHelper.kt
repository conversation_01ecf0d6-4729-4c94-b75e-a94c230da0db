package com.develop.mybible.data

import android.content.Context
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import android.util.Log
import java.io.File
import java.io.FileOutputStream

/**
 * Helper para acceder a la base de datos SQLite de la Biblia
 */
class BibleDatabaseHelper(private val context: Context) : SQLiteOpenHelper(
    context, DATABASE_NAME, null, DATABASE_VERSION
) {
    companion object {
        private const val TAG = "BibleDatabaseHelper"
        private const val DATABASE_NAME = "biblia.sqlite"
        private const val DATABASE_VERSION = 1

        // Nombre de las tablas
        const val TABLE_LIBROS = "libros"
        const val TABLE_CAPITULOS = "capitulos"
        const val TABLE_VERSICULOS = "versiculos"

        // Columnas para la tabla libros
        const val COLUMN_LIBRO_ID = "id"
        const val COLUMN_LIBRO_NOMBRE = "nombre"
        const val COLUMN_LIBRO_ABREVIACION = "abreviacion"
        const val COLUMN_LIBRO_TESTAMENTO = "testamento"
        const val COLUMN_LIBRO_URL = "url"

        // Columnas para la tabla capitulos
        const val COLUMN_CAPITULO_ID = "id"
        const val COLUMN_CAPITULO_LIBRO_ID = "libro_id"
        const val COLUMN_CAPITULO_NUMERO = "numero"
        const val COLUMN_CAPITULO_URL = "url"

        // Columnas para la tabla versiculos
        const val COLUMN_VERSICULO_ID = "id"
        const val COLUMN_VERSICULO_CAPITULO_ID = "capitulo_id"
        const val COLUMN_VERSICULO_NUMERO = "numero"
        const val COLUMN_VERSICULO_TEXTO = "texto"
    }

    private var database: SQLiteDatabase? = null

    init {
        // Asegurarse de que la base de datos existe
        checkDatabase()
    }

    /**
     * Verifica si la base de datos existe, y si no, la copia desde assets
     */
    private fun checkDatabase(): Boolean {
        val dbFile = context.getDatabasePath(DATABASE_NAME)
        
        if (!dbFile.exists()) {
            try {
                // Crear directorio si no existe
                dbFile.parentFile?.mkdirs()
                
                // Copiar la base de datos desde assets
                val inputStream = context.assets.open(DATABASE_NAME)
                val outputStream = FileOutputStream(dbFile)

                // Transferir bytes desde el inputStream al outputStream
                val buffer = ByteArray(1024)
                var length: Int
                while (inputStream.read(buffer).also { length = it } > 0) {
                    outputStream.write(buffer, 0, length)
                }

                // Cerrar los streams
                outputStream.flush()
                outputStream.close()
                inputStream.close()
                
                Log.i(TAG, "Base de datos copiada con éxito")
                return true
            } catch (e: Exception) {
                Log.e(TAG, "Error al copiar la base de datos", e)
                return false
            }
        }
        return true
    }

    /**
     * Obtiene una instancia de la base de datos
     */
    fun openDatabase(): SQLiteDatabase? {
        if (database == null || !database!!.isOpen) {
            database = SQLiteDatabase.openDatabase(
                context.getDatabasePath(DATABASE_NAME).path,
                null,
                SQLiteDatabase.OPEN_READWRITE
            )
        }
        return database
    }

    /**
     * Cierra la base de datos
     */
    override fun close() {
        if (database != null && database!!.isOpen) {
            database!!.close()
        }
        super.close()
    }

    override fun onCreate(db: SQLiteDatabase) {
        // No es necesario implementar ya que la base de datos viene pre-creada
    }

    override fun onUpgrade(db: SQLiteDatabase, oldVersion: Int, newVersion: Int) {
        // No es necesario implementar ya que la base de datos viene pre-creada
    }
} 