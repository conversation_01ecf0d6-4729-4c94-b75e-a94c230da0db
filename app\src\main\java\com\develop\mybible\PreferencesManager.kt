package com.develop.mybible

import android.content.Context
import android.content.SharedPreferences
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontFamily

/**
 * Clase para gestionar las preferencias persistentes de la aplicación
 */
open class PreferencesManager(context: Context?) {

    private val sharedPreferences: SharedPreferences? = context?.getSharedPreferences(
        PREF_NAME, Context.MODE_PRIVATE
    )

    /**
     * Guarda la preferencia de tema oscuro/claro
     */
    open fun setDarkTheme(isDarkTheme: Boolean) {
        sharedPreferences?.edit()?.putBoolean(KEY_DARK_THEME, isDarkTheme)?.apply()
    }

    /**
     * Obtiene la preferencia de tema oscuro/claro
     * @return true para tema oscuro (valor predeterminado), false para tema claro
     */
    open fun isDarkTheme(): Boolean {
        return sharedPreferences?.getBoolean(KEY_DARK_THEME, true) ?: true // true es el valor predeterminado (tema oscuro)
    }

    /**
     * Guarda el nombre amigable de la voz seleccionada
     */
    open fun setSelectedVoice(voiceName: String?) {
        if (voiceName != null) {
            sharedPreferences?.edit()?.putString(KEY_SELECTED_VOICE, voiceName)?.apply()
        }
    }

    /**
     * Obtiene el nombre amigable de la voz seleccionada
     */
    open fun getSelectedVoice(): String? {
        return sharedPreferences?.getString(KEY_SELECTED_VOICE, null)
    }

    /**
     * Guarda la velocidad de reproducción
     * Nota: Método mantenido por compatibilidad pero no guarda nada
     */
    open fun setSpeechRate(rate: Float) {
        // No guardamos la velocidad, siempre usamos 1.0f por defecto
        // Este método se mantiene por compatibilidad
    }

    /**
     * Obtiene la velocidad de reproducción
     * Nota: Método mantenido por compatibilidad pero siempre devuelve 1.0f
     */
    open fun getSpeechRate(): Float {
        // Siempre devolvemos 1.0f como valor por defecto
        return 1.0f
    }

    /**
     * Guarda el tono de reproducción
     * Nota: Método mantenido por compatibilidad pero no guarda nada
     */
    open fun setSpeechPitch(pitch: Float) {
        // No guardamos el tono, siempre usamos 1.0f por defecto
        // Este método se mantiene por compatibilidad
    }

    /**
     * Obtiene el tono de reproducción
     * Nota: Método mantenido por compatibilidad pero siempre devuelve 1.0f
     */
    open fun getSpeechPitch(): Float {
        // Siempre devolvemos 1.0f como valor por defecto
        return 1.0f
    }

    /**
     * Guarda la última posición de lectura (libro, capítulo, versículo)
     */
    open fun saveLastReadingPosition(book: String, chapter: String, verse: String) {
        sharedPreferences?.edit()
            ?.putString(KEY_LAST_BOOK, book)
            ?.putString(KEY_LAST_CHAPTER, chapter)
            ?.putString(KEY_LAST_VERSE, verse)
            ?.apply()
    }

    /**
     * Obtiene el último libro leído
     */
    open fun getLastBook(): String {
        return sharedPreferences?.getString(KEY_LAST_BOOK, "Génesis") ?: "Génesis"
    }

    /**
     * Obtiene el último capítulo leído
     */
    open fun getLastChapter(): String {
        return sharedPreferences?.getString(KEY_LAST_CHAPTER, "1") ?: "1"
    }

    /**
     * Obtiene el último versículo leído
     */
    open fun getLastVerse(): String {
        return sharedPreferences?.getString(KEY_LAST_VERSE, "1") ?: "1"
    }

    /**
     * Guarda el estado de onboarding (si el usuario ya lo completó)
     */
    open fun saveOnboardingState(completed: Boolean) {
        sharedPreferences?.edit()?.putBoolean(KEY_ONBOARDING_COMPLETED, completed)?.apply()
    }

    /**
     * Verifica si el usuario ya completó el onboarding
     */
    open fun isOnboardingCompleted(): Boolean {
        return sharedPreferences?.getBoolean(KEY_ONBOARDING_COMPLETED, false) ?: false
    }

    /**
     * Guarda el tamaño de texto para la lectura
     */
    open fun setTextSize(size: Int) {
        sharedPreferences?.edit()?.putInt(KEY_TEXT_SIZE, size)?.apply()
    }

    /**
     * Obtiene el tamaño de texto para la lectura
     */
    open fun getTextSize(): Int {
        return sharedPreferences?.getInt(KEY_TEXT_SIZE, 18) ?: 18 // 18sp es el valor predeterminado
    }

    /**
     * Guarda el tipo de fuente para la lectura
     * Nota: Solo guardamos el índice de la fuente, ya que FontFamily no es serializable
     */
    open fun setFontFamily(fontFamily: FontFamily) {
        val fontIndex = when(fontFamily) {
            FontFamily.SansSerif -> 0
            FontFamily.Serif -> 1
            FontFamily.Monospace -> 2
            FontFamily.Cursive -> 3
            else -> 0 // Default a SansSerif
        }
        sharedPreferences?.edit()?.putInt(KEY_FONT_FAMILY, fontIndex)?.apply()
    }

    /**
     * Obtiene el tipo de fuente para la lectura
     */
    open fun getFontFamily(): FontFamily {
        return when(sharedPreferences?.getInt(KEY_FONT_FAMILY, 0) ?: 0) {
            0 -> FontFamily.SansSerif
            1 -> FontFamily.Serif
            2 -> FontFamily.Monospace
            3 -> FontFamily.Cursive
            else -> FontFamily.SansSerif
        }
    }

    /**
     * Guarda el color de resaltado
     * Nota: Guardamos el valor como un string hexadecimal
     */
    open fun setHighlightColor(color: Color) {
        // Convertimos el color a un string hexadecimal
        val colorString = String.format("#%08X", color.value.toLong())
        sharedPreferences?.edit()?.putString(KEY_HIGHLIGHT_COLOR, colorString)?.apply()
    }

    /**
     * Obtiene el color de resaltado
     */
    open fun getHighlightColor(): Color {
        val defaultColorString = "#FFFFEB3B" // Amarillo por defecto
        val colorString = sharedPreferences?.getString(KEY_HIGHLIGHT_COLOR, defaultColorString) ?: defaultColorString

        // Convertimos el string hexadecimal a un color
        return try {
            Color(android.graphics.Color.parseColor(colorString))
        } catch (e: Exception) {
            Color(0xFFFFEB3B) // Valor por defecto en caso de error
        }
    }

    companion object {
        private const val PREF_NAME = "mybible_preferences"
        private const val KEY_DARK_THEME = "dark_theme"
        private const val KEY_SELECTED_VOICE = "selected_voice"
        private const val KEY_SPEECH_RATE = "speech_rate"
        private const val KEY_SPEECH_PITCH = "speech_pitch"
        private const val KEY_LAST_BOOK = "last_book"
        private const val KEY_LAST_CHAPTER = "last_chapter"
        private const val KEY_LAST_VERSE = "last_verse"
        private const val KEY_ONBOARDING_COMPLETED = "onboarding_completed"
        private const val KEY_TEXT_SIZE = "text_size"
        private const val KEY_FONT_FAMILY = "font_family"
        private const val KEY_HIGHLIGHT_COLOR = "highlight_color"
    }
}