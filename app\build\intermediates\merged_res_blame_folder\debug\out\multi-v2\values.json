{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.develop.mybible.app-mergeDebugResources-49:\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\039f90c30800cf165db0f833f096a0f1\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,6,7,9,10,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,81,82,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,107,114,115,116,117,118,119,120,176,205,206,210,211,215,220,221", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,412,477,600,669,1054,1124,1192,1264,1334,1395,1469,1542,1603,1664,1726,1790,1852,1913,1981,2081,2141,2207,2280,2349,2406,2458,2520,2592,2668,4990,5025,5222,5277,5340,5395,5453,5511,5572,5635,5692,5743,5793,5854,5911,5977,6011,6046,6341,6862,6929,7001,7070,7139,7213,7285,11014,12623,12740,12941,13051,13252,13546,13618", "endLines": "2,3,4,6,7,9,10,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,81,82,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,107,114,115,116,117,118,119,120,176,205,209,210,214,215,220,221", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "205,277,365,472,538,664,727,1119,1187,1259,1329,1390,1464,1537,1598,1659,1721,1785,1847,1908,1976,2076,2136,2202,2275,2344,2401,2453,2515,2587,2663,2728,5020,5055,5272,5335,5390,5448,5506,5567,5630,5687,5738,5788,5849,5906,5972,6006,6041,6076,6406,6924,6996,7065,7134,7208,7280,7368,11080,12735,12936,13046,13247,13376,13613,13680"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\51cf6b2c8dd9298f5d541d6fb0952380\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "80,84", "startColumns": "4,4", "startOffsets": "4936,5113", "endColumns": "53,66", "endOffsets": "4985,5175"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\0f327f3b2e33f04366e0af4c09726a8d\\transformed\\lifecycle-viewmodel-2.6.2\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "105", "startColumns": "4", "startOffsets": "6238", "endColumns": "49", "endOffsets": "6283"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\9a20719684b6f7dd9e6d4ef09eed6e5d\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "108", "startColumns": "4", "startOffsets": "6411", "endColumns": "82", "endOffsets": "6489"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Android Studio (Projects)\\MyBible\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endLines": "6", "endColumns": "12", "endOffsets": "294"}, "to": {"startLines": "216", "startColumns": "4", "startOffsets": "13381", "endLines": "219", "endColumns": "12", "endOffsets": "13541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\084a2c6004ec8b69e4406c583675f216\\transformed\\material3-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "110,111,112,113,123,124,125,126,127,128,131,132,133,134,135,136,137,138,139,140,141,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,161,163,166,173,175,177,181,182,183,184,185,186,187,188,189,190,191,192,193,194", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6539,6623,6705,6782,7492,7540,7601,7680,7782,7864,7980,8030,8095,8152,8217,8302,8393,8463,8556,8645,8739,8884,8971,9055,9147,9241,9301,9365,9448,9538,9601,9669,9737,9834,9939,10011,10243,10345,10503,10862,10961,11085,11326,11372,11422,11489,11556,11622,11687,11741,11813,11880,11950,12032,12078,12144", "endLines": "110,111,112,113,123,124,125,126,127,130,131,132,133,134,135,136,137,138,139,140,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,161,163,166,173,175,177,181,182,183,184,185,186,187,188,189,190,191,192,193,194", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "6618,6700,6777,6857,7535,7596,7675,7777,7859,7975,8025,8090,8147,8212,8297,8388,8458,8551,8640,8734,8879,8966,9050,9142,9236,9296,9360,9443,9533,9596,9664,9732,9829,9934,10006,10071,10282,10386,10567,10910,11009,11148,11367,11417,11484,11551,11617,11682,11736,11808,11875,11945,12027,12073,12139,12200"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\216db8d0a30175238f1d931ff9b405d8\\transformed\\activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "85,103", "startColumns": "4,4", "startOffsets": "5180,6124", "endColumns": "41,59", "endOffsets": "5217,6179"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Android Studio (Projects)\\MyBible\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "5,11,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "370,732,779,826,873,918,963,1012", "endColumns": "41,46,46,46,44,44,48,41", "endOffsets": "407,774,821,868,913,958,1007,1049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\d581dbda262f0aafb7c2311e84ff7a40\\transformed\\lifecycle-runtime-2.6.2\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "6081", "endColumns": "42", "endOffsets": "6119"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\7601f88b2cef2a4a7125135068095837\\transformed\\navigation-runtime-2.7.5\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "83", "startColumns": "4", "startOffsets": "5060", "endColumns": "52", "endOffsets": "5108"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Android Studio (Projects)\\MyBible\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "109", "startColumns": "4", "startOffsets": "6494", "endColumns": "44", "endOffsets": "6534"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\96b7aae757b73e215923b41989b590bc\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "6184", "endColumns": "53", "endOffsets": "6233"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\e8519d4f7af77f31265f93c93332bfd6\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,106,121,122,159,160,162,164,165,167,168,169,170,171,172,174,178,179,180,195,198,201", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2733,2792,2851,2911,2971,3031,3091,3151,3211,3271,3331,3391,3451,3510,3570,3630,3690,3750,3810,3870,3930,3990,4050,4110,4169,4229,4289,4348,4407,4466,4525,4584,4643,4717,4775,4830,4881,6288,7373,7438,10076,10142,10287,10391,10443,10572,10634,10688,10724,10758,10808,10915,11153,11200,11236,12205,12317,12428", "endLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,106,121,122,159,160,162,164,165,167,168,169,170,171,172,174,178,179,180,197,200,204", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "2787,2846,2906,2966,3026,3086,3146,3206,3266,3326,3386,3446,3505,3565,3625,3685,3745,3805,3865,3925,3985,4045,4105,4164,4224,4284,4343,4402,4461,4520,4579,4638,4712,4770,4825,4876,4931,6336,7433,7487,10137,10238,10340,10438,10498,10629,10683,10719,10753,10803,10857,10956,11195,11231,11321,12312,12423,12618"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Android Studio (Projects)\\MyBible\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "8", "startColumns": "4", "startOffsets": "543", "endColumns": "56", "endOffsets": "595"}}]}, {"outputFile": "com.develop.mybible.app-mergeDebugResources-49:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\039f90c30800cf165db0f833f096a0f1\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,6,7,9,10,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,81,82,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,107,114,115,116,117,118,119,120,176,205,206,210,211,215,220,221", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,210,282,412,477,600,669,1054,1124,1192,1264,1334,1395,1469,1542,1603,1664,1726,1790,1852,1913,1981,2081,2141,2207,2280,2349,2406,2458,2520,2592,2668,4990,5025,5222,5277,5340,5395,5453,5511,5572,5635,5692,5743,5793,5854,5911,5977,6011,6046,6341,6865,6932,7004,7073,7142,7216,7288,11017,12626,12743,12944,13054,13255,13552,13624", "endLines": "2,3,4,6,7,9,10,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,81,82,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,107,114,115,116,117,118,119,120,176,205,209,210,214,215,220,221", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "205,277,365,472,538,664,727,1119,1187,1259,1329,1390,1464,1537,1598,1659,1721,1785,1847,1908,1976,2076,2136,2202,2275,2344,2401,2453,2515,2587,2663,2728,5020,5055,5272,5335,5390,5448,5506,5567,5630,5687,5738,5788,5849,5906,5972,6006,6041,6076,6406,6927,6999,7068,7137,7211,7283,7371,11083,12738,12939,13049,13250,13379,13619,13686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\51cf6b2c8dd9298f5d541d6fb0952380\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "80,84", "startColumns": "4,4", "startOffsets": "4936,5113", "endColumns": "53,66", "endOffsets": "4985,5175"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\0f327f3b2e33f04366e0af4c09726a8d\\transformed\\lifecycle-viewmodel-2.6.2\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "105", "startColumns": "4", "startOffsets": "6238", "endColumns": "49", "endOffsets": "6283"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\9a20719684b6f7dd9e6d4ef09eed6e5d\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "108", "startColumns": "4", "startOffsets": "6411", "endColumns": "82", "endOffsets": "6489"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Android Studio (Projects)\\MyBible\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endLines": "6", "endColumns": "12", "endOffsets": "297"}, "to": {"startLines": "216", "startColumns": "4", "startOffsets": "13384", "endLines": "219", "endColumns": "12", "endOffsets": "13547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\084a2c6004ec8b69e4406c583675f216\\transformed\\material3-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "110,111,112,113,123,124,125,126,127,128,131,132,133,134,135,136,137,138,139,140,141,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,161,163,166,173,175,177,181,182,183,184,185,186,187,188,189,190,191,192,193,194", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6542,6626,6708,6785,7495,7543,7604,7683,7785,7867,7983,8033,8098,8155,8220,8305,8396,8466,8559,8648,8742,8887,8974,9058,9150,9244,9304,9368,9451,9541,9604,9672,9740,9837,9942,10014,10246,10348,10506,10865,10964,11088,11329,11375,11425,11492,11559,11625,11690,11744,11816,11883,11953,12035,12081,12147", "endLines": "110,111,112,113,123,124,125,126,127,130,131,132,133,134,135,136,137,138,139,140,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,161,163,166,173,175,177,181,182,183,184,185,186,187,188,189,190,191,192,193,194", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "6621,6703,6780,6860,7538,7599,7678,7780,7862,7978,8028,8093,8150,8215,8300,8391,8461,8554,8643,8737,8882,8969,9053,9145,9239,9299,9363,9446,9536,9599,9667,9735,9832,9937,10009,10074,10285,10389,10570,10913,11012,11151,11370,11420,11487,11554,11620,11685,11739,11811,11878,11948,12030,12076,12142,12203"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\216db8d0a30175238f1d931ff9b405d8\\transformed\\activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "85,103", "startColumns": "4,4", "startOffsets": "5180,6124", "endColumns": "41,59", "endOffsets": "5217,6179"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Android Studio (Projects)\\MyBible\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "5,11,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "370,732,779,826,873,918,963,1012", "endColumns": "41,46,46,46,44,44,48,41", "endOffsets": "407,774,821,868,913,958,1007,1049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\d581dbda262f0aafb7c2311e84ff7a40\\transformed\\lifecycle-runtime-2.6.2\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "102", "startColumns": "4", "startOffsets": "6081", "endColumns": "42", "endOffsets": "6119"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\7601f88b2cef2a4a7125135068095837\\transformed\\navigation-runtime-2.7.5\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "83", "startColumns": "4", "startOffsets": "5060", "endColumns": "52", "endOffsets": "5108"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Android Studio (Projects)\\MyBible\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "47", "endOffsets": "59"}, "to": {"startLines": "109", "startColumns": "4", "startOffsets": "6494", "endColumns": "47", "endOffsets": "6537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\96b7aae757b73e215923b41989b590bc\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "104", "startColumns": "4", "startOffsets": "6184", "endColumns": "53", "endOffsets": "6233"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\e8519d4f7af77f31265f93c93332bfd6\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,106,121,122,159,160,162,164,165,167,168,169,170,171,172,174,178,179,180,195,198,201", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2733,2792,2851,2911,2971,3031,3091,3151,3211,3271,3331,3391,3451,3510,3570,3630,3690,3750,3810,3870,3930,3990,4050,4110,4169,4229,4289,4348,4407,4466,4525,4584,4643,4717,4775,4830,4881,6288,7376,7441,10079,10145,10290,10394,10446,10575,10637,10691,10727,10761,10811,10918,11156,11203,11239,12208,12320,12431", "endLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,106,121,122,159,160,162,164,165,167,168,169,170,171,172,174,178,179,180,197,200,204", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "2787,2846,2906,2966,3026,3086,3146,3206,3266,3326,3386,3446,3505,3565,3625,3685,3745,3805,3865,3925,3985,4045,4105,4164,4224,4284,4343,4402,4461,4520,4579,4638,4712,4770,4825,4876,4931,6336,7436,7490,10140,10241,10343,10441,10501,10632,10686,10722,10756,10806,10860,10959,11198,11234,11324,12315,12426,12621"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Documentos\\Android Studio (Projects)\\MyBible\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "8", "startColumns": "4", "startOffsets": "543", "endColumns": "56", "endOffsets": "595"}}]}]}