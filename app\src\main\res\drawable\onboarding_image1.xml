<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="200dp"
    android:viewportWidth="200"
    android:viewportHeight="200">
    
    <!-- Fondo circular claro -->
    <path
        android:fillColor="#E0F7FA"
        android:pathData="M100,100m-80,0a80,80 0,1 1,160 0a80,80 0,1 1,-160 0" />
    
    <!-- Laptop/Monitor -->
    <path
        android:fillColor="#0E7C86"
        android:pathData="M50,70L150,70L150,120L50,120Z" />
    
    <!-- Pan<PERSON><PERSON> del monitor -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M55,75L145,75L145,115L55,115Z" />
    
    <!-- Texto "SEARCH" -->
    <path
        android:fillColor="#0E7C86"
        android:pathData="M70,95L130,95L130,100L70,100Z" />
    
    <!-- Base del monitor -->
    <path
        android:fillColor="#0E7C86"
        android:pathData="M90,120L110,120L110,130L90,130Z" />
    
    <!-- Persona a la izquierda (simplificada) -->
    <path
        android:fillColor="#FF8A65"
        android:pathData="M40,110m-15,0a15,15 0,1 1,30 0a15,15 0,1 1,-30 0" />
    
    <!-- Persona a la derecha (simplificada) -->
    <path
        android:fillColor="#EF5350"
        android:pathData="M160,110m-15,0a15,15 0,1 1,30 0a15,15 0,1 1,-30 0" />
    
    <!-- Hoja decorativa 1 -->
    <path
        android:fillColor="#80CBC4"
        android:pathData="M30,60C40,50 20,30 10,40C20,50 20,70 30,60Z" />
    
    <!-- Hoja decorativa 2 -->
    <path
        android:fillColor="#4DB6AC"
        android:pathData="M170,60C160,50 180,30 190,40C180,50 180,70 170,60Z" />
</vector> 