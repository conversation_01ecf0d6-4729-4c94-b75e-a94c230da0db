1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.develop.mybible"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Permisos para cuando implementemos acceso a internet para obtener textos bíblicos -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:7:22-76
14
15    <permission
15-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
16        android:name="com.develop.mybible.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.develop.mybible.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
20
21    <application
21-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:9:5-30:19
22        android:allowBackup="true"
22-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:10:9-35
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
24        android:dataExtractionRules="@xml/data_extraction_rules"
24-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:11:9-65
25        android:debuggable="true"
26        android:extractNativeLibs="false"
27        android:fullBackupContent="@xml/backup_rules"
27-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:12:9-54
28        android:icon="@mipmap/ic_launcher"
28-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:13:9-43
29        android:label="@string/app_name"
29-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:14:9-41
30        android:roundIcon="@mipmap/ic_launcher_round"
30-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:15:9-54
31        android:supportsRtl="true"
31-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:16:9-35
32        android:testOnly="true"
33        android:theme="@style/Theme.OpenBiblia" >
33-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:17:9-48
34        <activity
34-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:19:9-29:20
35            android:name="com.develop.mybible.MainActivity"
35-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:20:13-41
36            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
36-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:22:13-87
37            android:exported="true"
37-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:21:13-36
38            android:theme="@style/Theme.OpenBiblia"
38-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:24:13-52
39            android:windowSoftInputMode="adjustResize" >
39-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:23:13-55
40            <intent-filter>
40-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:25:13-28:29
41                <action android:name="android.intent.action.MAIN" />
41-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:26:17-69
41-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:26:25-66
42
43                <category android:name="android.intent.category.LAUNCHER" />
43-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:27:17-77
43-->C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:27:27-74
44            </intent-filter>
45        </activity>
46        <activity
46-->[androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\860fd76c880d9e09a864fdbfcc0048a7\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
47            android:name="androidx.compose.ui.tooling.PreviewActivity"
47-->[androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\860fd76c880d9e09a864fdbfcc0048a7\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
48            android:exported="true" />
48-->[androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\860fd76c880d9e09a864fdbfcc0048a7\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
49
50        <provider
50-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
51            android:name="androidx.startup.InitializationProvider"
51-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
52            android:authorities="com.develop.mybible.androidx-startup"
52-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
53            android:exported="false" >
53-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
54            <meta-data
54-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
55                android:name="androidx.emoji2.text.EmojiCompatInitializer"
55-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
56                android:value="androidx.startup" />
56-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
57            <meta-data
57-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\6d9a30476eb0a9d76f1baf7d3f58eb97\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
58-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\6d9a30476eb0a9d76f1baf7d3f58eb97\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
59                android:value="androidx.startup" />
59-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\6d9a30476eb0a9d76f1baf7d3f58eb97\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
60            <meta-data
60-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
61-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
62                android:value="androidx.startup" />
62-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
63        </provider>
64
65        <activity
65-->[androidx.compose.ui:ui-test-manifest:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd0de47c6caf39a023a0e1f57bbb36ce\transformed\ui-test-manifest-1.5.1\AndroidManifest.xml:23:9-25:39
66            android:name="androidx.activity.ComponentActivity"
66-->[androidx.compose.ui:ui-test-manifest:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd0de47c6caf39a023a0e1f57bbb36ce\transformed\ui-test-manifest-1.5.1\AndroidManifest.xml:24:13-63
67            android:exported="true" />
67-->[androidx.compose.ui:ui-test-manifest:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd0de47c6caf39a023a0e1f57bbb36ce\transformed\ui-test-manifest-1.5.1\AndroidManifest.xml:25:13-36
68
69        <receiver
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
70            android:name="androidx.profileinstaller.ProfileInstallReceiver"
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
71            android:directBootAware="false"
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
72            android:enabled="true"
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
73            android:exported="true"
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
74            android:permission="android.permission.DUMP" >
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
76                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
77            </intent-filter>
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
79                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
82                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
85                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
86            </intent-filter>
87        </receiver>
88    </application>
89
90</manifest>
