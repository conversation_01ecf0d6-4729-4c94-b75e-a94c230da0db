INSTRUCCIONES PARA INCLUIR LA BASE DE DATOS SQLITE

La aplicación Mi Biblia está diseñada para utilizar una base de datos SQLite que contiene
todos los libros, capítulos y versículos de la Biblia.

Pasos para incluir la base de datos:

1. Coloca tu archivo de base de datos SQLite con el nombre "biblia.sqlite" en este directorio
   (app/src/main/assets/)

2. Asegúrate de que la base de datos tenga la siguiente estructura:

   a) Tabla "libros" con columnas:
      - id (INTEGER): Identificador único del libro
      - nombre (TEXT): Nombre completo del libro (por ejemplo, "Génesis")
      - abreviacion (TEXT): Abreviación del libro (por ejemplo, "Gen")
      - testamento (TEXT): "Antiguo" o "Nuevo"
      - url (TEXT): URL relacionada (opcional)

   b) Tabla "capitulos" con columnas:
      - id (INTEGER): Identificador único del capítulo
      - libro_id (INTEGER): ID del libro al que pertenece el capítulo (clave foránea a libros.id)
      - numero (TEXT): Número del capítulo
      - url (TEXT): URL relacionada (opcional)

   c) Tabla "versiculos" con columnas:
      - id (INTEGER): Identificador único del versículo
      - capitulo_id (INTEGER): ID del capítulo al que pertenece el versículo (clave foránea a capitulos.id)
      - numero (TEXT): Número del versículo
      - texto (TEXT): Texto completo del versículo

3. La aplicación copiará automáticamente esta base de datos al directorio de la aplicación
   la primera vez que se ejecute.

NOTAS:
- Asegúrate de que la base de datos esté correctamente formateada antes de incluirla.
- Si necesitas actualizar la base de datos, simplemente reemplaza el archivo en este directorio
  y desinstala/reinstala la aplicación para que se copie la nueva versión. 