<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="200dp"
    android:viewportWidth="200"
    android:viewportHeight="200">
    
    <!-- Fondo circular claro -->
    <path
        android:fillColor="#E0F7FA"
        android:pathData="M100,100m-80,0a80,80 0,1 1,160 0a80,80 0,1 1,-160 0" />
    
    <!-- Libro abierto -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M50,70L150,120L150,130L50,80Z" />
    
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M150,70L50,120L50,130L150,80Z" />
    
    <!-- Líneas de texto en el libro -->
    <path
        android:fillColor="#0E7C86"
        android:pathData="M60,85L90,90L90,93L60,88Z" />
    
    <path
        android:fillColor="#0E7C86"
        android:pathData="M60,95L90,100L90,103L60,98Z" />
    
    <path
        android:fillColor="#0E7C86"
        android:pathData="M110,95L140,90L140,93L110,98Z" />
    
    <path
        android:fillColor="#0E7C86"
        android:pathData="M110,105L140,100L140,103L110,108Z" />
    
    <!-- Persona leyendo a la izquierda (simplificada) -->
    <path
        android:fillColor="#FF8A65"
        android:pathData="M40,130m-15,0a15,15 0,1 1,30 0a15,15 0,1 1,-30 0" />
    
    <!-- Persona leyendo a la derecha (simplificada) -->
    <path
        android:fillColor="#EF5350"
        android:pathData="M160,130m-15,0a15,15 0,1 1,30 0a15,15 0,1 1,-30 0" />
    
    <!-- Hoja decorativa 1 -->
    <path
        android:fillColor="#80CBC4"
        android:pathData="M30,60C40,50 20,30 10,40C20,50 20,70 30,60Z" />
    
    <!-- Hoja decorativa 2 -->
    <path
        android:fillColor="#4DB6AC"
        android:pathData="M170,60C160,50 180,30 190,40C180,50 180,70 170,60Z" />
</vector> 