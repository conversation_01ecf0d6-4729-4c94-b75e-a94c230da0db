package com.develop.mybible.ui.settings

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import com.develop.mybible.PreferencesManager
import com.develop.mybible.ui.theme.MyBibleTheme

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onBackPressed: () -> Unit,
    isDarkTheme: Boolean,
    onThemeChange: (Boolean) -> Unit,
    preferencesManager: PreferencesManager
) {
    // Estado para las preferencias
    var textSize by remember { mutableStateOf(preferencesManager.getTextSize()) }
    var selectedFontFamily by remember { mutableStateOf(preferencesManager.getFontFamily()) }

    // Lista de tamaños de texto disponibles (con límites)
    val availableTextSizes = listOf(14, 16, 18, 20, 22, 24)

    // Lista de fuentes disponibles (del sistema)
    val availableFonts = listOf(
        "Sans Serif" to FontFamily.SansSerif,
        "Serif" to FontFamily.Serif,
        "Monospace" to FontFamily.Monospace,
        "Cursive" to FontFamily.Cursive,
        "Default" to FontFamily.Default
    )



    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Ajustes") },
                navigationIcon = {
                    IconButton(onClick = onBackPressed) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Volver"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
                .verticalScroll(rememberScrollState())
        ) {
            // Sección: Personalización de la lectura
            SectionTitle("Personalización de la lectura", isDarkTheme)

            // Tamaño de texto
            SettingItem(
                title = "Tamaño de texto",
                icon = Icons.Default.FormatSize,
                description = "${textSize}sp"
            ) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp)
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            "Selecciona el tamaño de texto",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Spacer(modifier = Modifier.height(16.dp))

                        // Muestra de texto con el tamaño seleccionado
                        Text(
                            "Ejemplo de texto con este tamaño",
                            fontSize = textSize.sp,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp)
                                .background(
                                    MaterialTheme.colorScheme.surfaceVariant,
                                    RoundedCornerShape(8.dp)
                                )
                                .padding(16.dp)
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        // Opciones de tamaño
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            availableTextSizes.forEach { size ->
                                TextSizeOption(
                                    size = size,
                                    isSelected = size == textSize,
                                    onClick = {
                                        textSize = size
                                        preferencesManager.setTextSize(size)
                                    }
                                )
                            }
                        }
                    }
                }
            }

            Divider(modifier = Modifier.padding(vertical = 8.dp))

            // Tipo de fuente
            SettingItem(
                title = "Tipo de fuente",
                icon = Icons.Default.TextFormat,
                description = availableFonts.find { it.second == selectedFontFamily }?.first ?: "Default"
            ) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp)
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            "Selecciona el tipo de fuente",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Spacer(modifier = Modifier.height(16.dp))

                        availableFonts.forEach { (name, fontFamily) ->
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clickable {
                                        selectedFontFamily = fontFamily
                                        preferencesManager.setFontFamily(fontFamily)
                                    }
                                    .padding(vertical = 12.dp, horizontal = 8.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                RadioButton(
                                    selected = fontFamily == selectedFontFamily,
                                    onClick = {
                                        selectedFontFamily = fontFamily
                                        preferencesManager.setFontFamily(fontFamily)
                                    }
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "Ejemplo con $name",
                                    fontFamily = fontFamily,
                                    fontSize = 16.sp
                                )
                            }
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Sección: Personalización de la interfaz
            SectionTitle("Personalización de la interfaz", isDarkTheme)

            // Tema de la aplicación
            SettingItem(
                title = "Tema de la aplicación",
                icon = if (isDarkTheme) Icons.Default.DarkMode else Icons.Default.LightMode,
                description = if (isDarkTheme) "Oscuro" else "Claro"
            ) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp)
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            "Selecciona el tema",
                            style = MaterialTheme.typography.titleMedium
                        )
                        Spacer(modifier = Modifier.height(16.dp))

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            // Opción tema claro
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                                modifier = Modifier
                                    .weight(1f)
                                    .clickable { onThemeChange(false) }
                                    .padding(8.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.LightMode,
                                    contentDescription = "Tema claro",
                                    modifier = Modifier
                                        .size(48.dp)
                                        .background(
                                            if (!isDarkTheme) MaterialTheme.colorScheme.primary else Color.Transparent,
                                            CircleShape
                                        )
                                        .padding(8.dp),
                                    tint = if (!isDarkTheme) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSurface
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                Text("Claro")
                            }

                            // Opción tema oscuro
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                                modifier = Modifier
                                    .weight(1f)
                                    .clickable { onThemeChange(true) }
                                    .padding(8.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.DarkMode,
                                    contentDescription = "Tema oscuro",
                                    modifier = Modifier
                                        .size(48.dp)
                                        .background(
                                            if (isDarkTheme) MaterialTheme.colorScheme.primary else Color.Transparent,
                                            CircleShape
                                        )
                                        .padding(8.dp),
                                    tint = if (isDarkTheme) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSurface
                                )
                                Spacer(modifier = Modifier.height(8.dp))
                                Text("Oscuro")
                            }
                        }
                    }
                }
            }



            Spacer(modifier = Modifier.height(16.dp))

            // Sección: Preferencias de contenido
            SectionTitle("Preferencias de contenido", isDarkTheme)

            // Versión de la Biblia
            SettingItem(
                title = "Versión de la Biblia",
                icon = Icons.Default.Book,
                description = "Biblia de Jerusalén"
            ) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp)
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            "Se agregarán más versiónes de la Biblia",
                            style = MaterialTheme.typography.bodyLarge,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Sección: Información
            SectionTitle("Información", isDarkTheme)

            // Versión de la aplicación (sin expandir)
            SettingItem(
                title = "Versión",
                icon = Icons.Default.Info,
                description = "v1.0.0 (1)"
            )

            Divider(modifier = Modifier.padding(vertical = 8.dp))

            // Creador
            SettingItem(
                title = "Creador",
                icon = Icons.Default.Person,
                description = "Tu Amigo Desarrollador"
            ) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp)
                ) {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            text = "\"En memoria de Carlo Acutis, el influencer de Dios, mi inspiración.\"",
                            style = MaterialTheme.typography.bodyLarge.copy(
                                fontStyle = FontStyle.Italic
                            ),
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(32.dp))
        }
    }
}

@Composable
fun SectionTitle(title: String, isDarkTheme: Boolean = false) {
    Text(
        text = title,
        style = MaterialTheme.typography.titleMedium.copy(
            fontWeight = FontWeight.Bold,
            color = if (isDarkTheme) MaterialTheme.colorScheme.primary else Color.Black
        ),
        modifier = Modifier.padding(vertical = 8.dp)
    )
}

@Composable
fun SettingItem(
    title: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    description: String,
    content: @Composable (() -> Unit)? = null
) {
    var expanded by remember { mutableStateOf(false) }

    Column {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { expanded = !expanded }
                .padding(vertical = 12.dp, horizontal = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(16.dp))

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleSmall
                )

                Text(
                    text = description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            if (content != null) {
                Icon(
                    imageVector = if (expanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                    contentDescription = if (expanded) "Contraer" else "Expandir",
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        if (expanded && content != null) {
            content()
        }
    }
}

@Composable
fun TextSizeOption(
    size: Int,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(48.dp)
            .clip(CircleShape)
            .background(
                if (isSelected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.surfaceVariant,
                CircleShape
            )
            .border(
                BorderStroke(
                    1.dp,
                    if (isSelected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.outline
                ),
                CircleShape
            )
            .clickable(onClick = onClick),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "$size",
            color = if (isSelected) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSurfaceVariant,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}



// Comentamos la vista previa por ahora para evitar errores
/*
@Preview(showBackground = true)
@Composable
fun SettingsScreenPreview() {
    // La vista previa se implementará más adelante
}
*/
