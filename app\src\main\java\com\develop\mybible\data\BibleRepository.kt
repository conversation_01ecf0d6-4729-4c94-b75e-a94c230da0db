package com.develop.mybible.data

import android.content.Context
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase

/**
 * Repositorio para acceder a los datos de la Biblia desde la base de datos SQLite
 */
class BibleRepository(context: Context) {
    private val dbHelper = BibleDatabaseHelper(context)
    private var database: SQLiteDatabase? = null

    /**
     * Obtiene todos los nombres de libros de la Biblia
     */
    fun getBookNames(): List<String> {
        val bookNames = mutableListOf<String>()
        database = dbHelper.openDatabase()

        val cursor = database?.query(
            BibleDatabaseHelper.TABLE_LIBROS,
            arrayOf(BibleDatabaseHelper.COLUMN_LIBRO_NOMBRE),
            null, null, null, null,
            "${BibleDatabaseHelper.COLUMN_LIBRO_ID} ASC"
        )

        cursor?.use {
            val columnIndex = it.getColumnIndex(BibleDatabaseHelper.COLUMN_LIBRO_NOMBRE)
            while (it.moveToNext()) {
                bookNames.add(it.getString(columnIndex))
            }
        }

        return bookNames
    }

    /**
     * Obtiene el ID de un libro por su nombre
     */
    private fun getBookIdByName(bookName: String): Int {
        var bookId = -1
        database = dbHelper.openDatabase()

        val cursor = database?.query(
            BibleDatabaseHelper.TABLE_LIBROS,
            arrayOf(BibleDatabaseHelper.COLUMN_LIBRO_ID),
            "${BibleDatabaseHelper.COLUMN_LIBRO_NOMBRE} = ?",
            arrayOf(bookName),
            null, null, null
        )

        cursor?.use {
            if (it.moveToFirst()) {
                bookId = it.getInt(it.getColumnIndex(BibleDatabaseHelper.COLUMN_LIBRO_ID))
            }
        }

        return bookId
    }

    /**
     * Obtiene los números de capítulos para un libro dado
     */
    fun getChapterNumbers(bookName: String): List<String> {
        val chapterNumbers = mutableListOf<String>()
        val bookId = getBookIdByName(bookName)
        if (bookId == -1) return chapterNumbers

        database = dbHelper.openDatabase()

        val cursor = database?.query(
            BibleDatabaseHelper.TABLE_CAPITULOS,
            arrayOf(BibleDatabaseHelper.COLUMN_CAPITULO_NUMERO),
            "${BibleDatabaseHelper.COLUMN_CAPITULO_LIBRO_ID} = ?",
            arrayOf(bookId.toString()),
            null, null,
            "${BibleDatabaseHelper.COLUMN_CAPITULO_NUMERO} ASC"
        )

        cursor?.use {
            val columnIndex = it.getColumnIndex(BibleDatabaseHelper.COLUMN_CAPITULO_NUMERO)
            while (it.moveToNext()) {
                chapterNumbers.add(it.getString(columnIndex))
            }
        }

        return chapterNumbers
    }

    /**
     * Obtiene el ID de un capítulo por su libro y número
     */
    private fun getChapterIdByBookAndNumber(bookId: Int, chapterNumber: String): Int {
        var chapterId = -1
        database = dbHelper.openDatabase()

        val cursor = database?.query(
            BibleDatabaseHelper.TABLE_CAPITULOS,
            arrayOf(BibleDatabaseHelper.COLUMN_CAPITULO_ID),
            "${BibleDatabaseHelper.COLUMN_CAPITULO_LIBRO_ID} = ? AND ${BibleDatabaseHelper.COLUMN_CAPITULO_NUMERO} = ?",
            arrayOf(bookId.toString(), chapterNumber),
            null, null, null
        )

        cursor?.use {
            if (it.moveToFirst()) {
                chapterId = it.getInt(it.getColumnIndex(BibleDatabaseHelper.COLUMN_CAPITULO_ID))
            }
        }

        return chapterId
    }

    /**
     * Obtiene los números de versículos para un libro y capítulo dados
     */
    fun getVerseNumbers(bookName: String, chapterNumber: String): List<String> {
        val verseNumbers = mutableListOf<String>()
        val bookId = getBookIdByName(bookName)
        if (bookId == -1) return verseNumbers
        
        val chapterId = getChapterIdByBookAndNumber(bookId, chapterNumber)
        if (chapterId == -1) return verseNumbers

        database = dbHelper.openDatabase()

        val cursor = database?.query(
            BibleDatabaseHelper.TABLE_VERSICULOS,
            arrayOf(BibleDatabaseHelper.COLUMN_VERSICULO_NUMERO),
            "${BibleDatabaseHelper.COLUMN_VERSICULO_CAPITULO_ID} = ?",
            arrayOf(chapterId.toString()),
            null, null,
            "${BibleDatabaseHelper.COLUMN_VERSICULO_NUMERO} ASC"
        )

        cursor?.use {
            val columnIndex = it.getColumnIndex(BibleDatabaseHelper.COLUMN_VERSICULO_NUMERO)
            while (it.moveToNext()) {
                verseNumbers.add(it.getString(columnIndex))
            }
        }

        return verseNumbers
    }

    /**
     * Obtiene todos los versículos para un libro y capítulo dados
     */
    fun getVersesForChapter(bookName: String, chapterNumber: String): Map<String, String> {
        val verses = mutableMapOf<String, String>()
        val bookId = getBookIdByName(bookName)
        if (bookId == -1) return verses
        
        val chapterId = getChapterIdByBookAndNumber(bookId, chapterNumber)
        if (chapterId == -1) return verses

        database = dbHelper.openDatabase()

        val cursor = database?.query(
            BibleDatabaseHelper.TABLE_VERSICULOS,
            arrayOf(
                BibleDatabaseHelper.COLUMN_VERSICULO_NUMERO,
                BibleDatabaseHelper.COLUMN_VERSICULO_TEXTO
            ),
            "${BibleDatabaseHelper.COLUMN_VERSICULO_CAPITULO_ID} = ?",
            arrayOf(chapterId.toString()),
            null, null,
            "${BibleDatabaseHelper.COLUMN_VERSICULO_NUMERO} ASC"
        )

        cursor?.use {
            val numeroIndex = it.getColumnIndex(BibleDatabaseHelper.COLUMN_VERSICULO_NUMERO)
            val textoIndex = it.getColumnIndex(BibleDatabaseHelper.COLUMN_VERSICULO_TEXTO)
            while (it.moveToNext()) {
                verses[it.getString(numeroIndex)] = it.getString(textoIndex)
            }
        }

        return verses
    }

    /**
     * Cierra la conexión con la base de datos
     */
    fun close() {
        dbHelper.close()
    }
} 