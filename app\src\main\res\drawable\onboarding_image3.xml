<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="200dp"
    android:viewportWidth="200"
    android:viewportHeight="200">
    
    <!-- Fondo circular claro -->
    <path
        android:fillColor="#E0F7FA"
        android:pathData="M100,100m-80,0a80,80 0,1 1,160 0a80,80 0,1 1,-160 0" />
    
    <!-- B<PERSON>lia cerrada -->
    <path
        android:fillColor="#0E7C86"
        android:pathData="M70,70L130,70L130,130L70,130Z" />
    
    <!-- Cubierta de la Biblia -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M75,75L125,75L125,125L75,125Z" />
    
    <!-- <PERSON> en la portada -->
    <path
        android:fillColor="#0E7C86"
        android:pathData="M95,85L105,85L105,115L95,115Z" />
    
    <path
        android:fillColor="#0E7C86"
        android:pathData="M85,95L115,95L115,105L85,105Z" />
    
    <!-- Marcador de la Biblia -->
    <path
        android:fillColor="#EF5350"
        android:pathData="M120,75L130,75L130,85L120,85Z" />
    
    <!-- Persona orando a la izquierda (simplificada) -->
    <path
        android:fillColor="#FF8A65"
        android:pathData="M40,110m-15,0a15,15 0,1 1,30 0a15,15 0,1 1,-30 0" />
    
    <!-- Persona estudiando a la derecha (simplificada) -->
    <path
        android:fillColor="#EF5350"
        android:pathData="M160,110m-15,0a15,15 0,1 1,30 0a15,15 0,1 1,-30 0" />
    
    <!-- Hoja decorativa 1 -->
    <path
        android:fillColor="#80CBC4"
        android:pathData="M30,60C40,50 20,30 10,40C20,50 20,70 30,60Z" />
    
    <!-- Hoja decorativa 2 -->
    <path
        android:fillColor="#4DB6AC"
        android:pathData="M170,60C160,50 180,30 190,40C180,50 180,70 170,60Z" />
</vector> 