package com.develop.mybible.ui.onboarding

import androidx.compose.ui.graphics.Color
import com.develop.mybible.R

/**
 * Modelo de datos para una página de onboarding
 */
data class OnboardingPage(
    val title: String,
    val description: String,
    val imageRes: Int,
    val backgroundColor: Color = Color(0xFF0E7C86) // Color teal del botón en la imagen
)

/**
 * Datos de ejemplo para las páginas de onboarding
 */
object OnboardingData {
    // Recursos de imágenes reales
    val page1 = OnboardingPage(
        title = "Moniter workflow",
        description = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore dolore magna aliqua. Ut enim ad.",
        imageRes = R.drawable.onboarding_image1
    )
    
    val page2 = OnboardingPage(
        title = "Explorar la Biblia",
        description = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore dolore magna aliqua. Ut enim ad.",
        imageRes = R.drawable.onboarding_image2
    )
    
    val page3 = OnboardingPage(
        title = "Estudiar la Palabra",
        description = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore dolore magna aliqua. Ut enim ad.",
        imageRes = R.drawable.onboarding_image3
    )
    
    val pages = listOf(page1, page2, page3)
} 