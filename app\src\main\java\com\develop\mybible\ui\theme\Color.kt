package com.develop.mybible.ui.theme

import androidx.compose.ui.graphics.Color

// Color dorado/ámbar usado en la pantalla de splash y botones
val BibleTeal = Color(0xFFFFBA39) // Cambiado de teal a dorado/ámbar
val BibleOnTeal = Color(0xFF000000) // Texto negro sobre dorado para mejor contraste

// Colores primarios y secundarios para la aplicación de Biblia
val BiblePrimary = Color(0xFFFFBA39) // Cambiado a dorado/ámbar
val BibleOnPrimary = Color(0xFF000000) // Texto negro sobre dorado para mejor contraste
val BiblePrimaryContainer = Color(0xFFFFF0D0) // Contenedor dorado claro
val BibleOnPrimaryContainer = Color(0xFF805C1D) // Texto dorado oscuro sobre contenedor

val BibleSecondary = Color(0xFFEE8F25) // Naranja secundario
val BibleOnSecondary = Color(0xFF000000) // Texto negro sobre naranja para mejor contraste
val BibleSecondaryContainer = Color(0xFFFFE5C2) // Contenedor naranja claro
val BibleOnSecondaryContainer = Color(0xFF804913) // Texto naranja oscuro sobre contenedor

val BibleTertiary = Color(0xFF795548) // Brown tertiary
val BibleOnTertiary = Color(0xFFFFFFFF) // White text on tertiary

// Colores para modo claro
val BibleLightBackground = Color(0xFFF8F8F8) // Light grey background
val BibleOnLightBackground = Color(0xFF000000) // Black text on background
val BibleLightSurface = Color(0xFFFFFFFF) // White surface
val BibleOnLightSurface = Color(0xFF000000) // Black text on surface

// Colores para modo oscuro
val BibleDarkBackground = Color(0xFF121212) // Dark background
val BibleOnDarkBackground = Color(0xFFE0E0E0) // Light text on background
val BibleDarkSurface = Color(0xFF1E1E1E) // Dark surface
val BibleOnDarkSurface = Color(0xFFE0E0E0) // Light text on surface

// Error colors
val BibleErrorColor = Color(0xFFB00020)
val BibleOnErrorColor = Color(0xFFFFFFFF)

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)