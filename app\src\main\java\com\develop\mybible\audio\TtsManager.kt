package com.develop.mybible.audio

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.speech.tts.TextToSpeech
import android.speech.tts.UtteranceProgressListener
import android.speech.tts.Voice
import android.util.Log
import java.util.Locale
import java.util.UUID
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * Clase que gestiona la funcionalidad de Text-to-Speech para leer versículos de la Biblia
 * Implementa una estrategia de fallback jerárquica para garantizar compatibilidad
 * en diferentes dispositivos y versiones de Android.
 */
open class TtsManager(private val context: Context?) {
    private var textToSpeech: TextToSpeech? = null
    private var isInitialized = false
    private var isInitializing = false
    private var isPlayingState = false
    private var currentSpeed = 1.0f
    private var currentPitch = 1.0f
    private var currentLocale = Locale("es", "ES") // Español por defecto
    private var availableVoices = listOf<Voice>()
    private var currentVoice: Voice? = null

    // Mapa para almacenar relación entre nombres simplificados y voces reales
    private val voiceNameMap = mutableMapOf<String, Voice>()

    // Callbacks para eventos de reproducción y errores
    var onPlaybackStarted: (() -> Unit)? = null
    var onPlaybackStopped: (() -> Unit)? = null
    var onPlaybackCompleted: (() -> Unit)? = null
    var onVerseChanged: ((String) -> Unit)? = null
    var onVoicesInitialized: ((List<Voice>) -> Unit)? = null
    var onTtsError: ((TtsError) -> Unit)? = null

    // Nuevo: Flujo de estado para el versículo actual siendo leído
    private val _currentVerseFlow = MutableStateFlow<String?>(null)
    val currentVerseFlow: StateFlow<String?> = _currentVerseFlow.asStateFlow()

    // Estado de TTS
    private val _ttsStateFlow = MutableStateFlow<TtsState>(TtsState.NotInitialized)
    val ttsStateFlow: StateFlow<TtsState> = _ttsStateFlow.asStateFlow()

    // Estado de procesamiento (para indicador visual de debounce)
    private val _isProcessingFlow = MutableStateFlow(false)
    val isProcessingFlow: StateFlow<Boolean> = _isProcessingFlow.asStateFlow()

    // Mapeo específico de identificadores de voz a nombres amigables
    // Ampliado con más voces comunes en dispositivos populares
    private val specificVoiceMap = linkedMapOf(
        // Voces españolas predefinidas
        "es-es-x-eef-local" to "Voz 1 (es)",
        "es-us-x-esd-local" to "Voz 2 (es)",
        "es-us-x-esf-local" to "Voz 3 (es)",
        "es-es-x-ems-local" to "Voz 4 (es)",
        "es-es-x-pep-local" to "Voz 5 (es)",
        "es-mx-x-dfc-local" to "Voz 6 (es)",
        "es-es-x-eec-local" to "Voz 7 (es)",
        "es-us-language" to "Voz 8 (es)",
        "es-es-x-eed-local" to "Voz 9 (es)",
        "es-es-x-ana-local" to "Voz 10 (es)",
        "es-mx-x-cab-local" to "Voz 11 (es)"
    )

    // Mapa para mantener un seguimiento de qué versos se están reproduciendo
    private var versesMap: Map<String, String> = mapOf()
    private var currentVerseNumber: String? = null
    private var pendingVerses: List<String> = listOf()

    // Caché para evitar reinicializaciones innecesarias
    private var hasCheckedVoiceSupport = false

    // Control de debounce/throttle para evitar múltiples acciones de reproducción en corto tiempo
    private var lastPlaybackTime = 0L
    private val debounceTime = 300L // 300ms entre acciones de reproducción (reducido de 1500ms)

    // Bloqueo durante el procesamiento de solicitudes TTS
    private var isProcessingTtsRequest = false

    // Tiempo de enfriamiento después de detener la reproducción (eliminado)
    private val cooldownTime = 0L // Eliminado el tiempo de enfriamiento

    // Variable para rastrear si una detención fue intencional (simplificado)
    private var isIntentionalStop = false
    private val intentionalStopResetTime = 300L // 300ms para resetear el estado de detención intencional (reducido de 1000ms)

    // Versículo actual en reproducción para filtrar eventos onStop irrelevantes
    private var activeUtteranceId: String? = null

    /**
     * Estados posibles del motor TTS
     */
    enum class TtsState {
        NotInitialized,
        Initializing,
        Ready,
        Error,
        NoVoicesAvailable,
        LanguageNotSupported
    }

    /**
     * Tipos de errores que pueden ocurrir con TTS
     */
    enum class TtsError {
        INITIALIZATION_FAILED,
        LANGUAGE_UNAVAILABLE,
        NO_VOICES_AVAILABLE,
        PLAYBACK_ERROR,
        NETWORK_UNAVAILABLE, // Nuevo error para cuando la voz requiere internet pero no hay conexión
        UNKNOWN_ERROR
    }

    init {
        if (context != null) {
            // Inicialización lazy - solo cuando se necesite
            _ttsStateFlow.value = TtsState.NotInitialized
        }
    }

    /**
     * Inicializa el TTS bajo demanda si aún no está inicializado
     * @return true si ya está inicializado o se inició la inicialización, false si hubo un error
     */
    fun ensureInitialized(): Boolean {
        if (context == null) {
            Log.e(TAG, "Error: Contexto nulo al inicializar TTS")
            return false
        }

        // Si ya está inicializado, asegurarse de que los valores actuales estén aplicados
        if (isInitialized) {
            // Verificar que el objeto TextToSpeech siga siendo válido
            if (textToSpeech == null) {
                Log.e(TAG, "Error: TextToSpeech es nulo a pesar de estar marcado como inicializado")
                isInitialized = false
                return ensureInitialized() // Reintentar inicialización
            }

            // Aplicar los valores actuales para asegurar consistencia
            try {
                textToSpeech?.setSpeechRate(currentSpeed)
                textToSpeech?.setPitch(currentPitch)
                Log.d(TAG, "TTS ya inicializado, aplicando valores actuales: velocidad=$currentSpeed, tono=$currentPitch")
            } catch (e: Exception) {
                Log.e(TAG, "Error al aplicar valores a TTS ya inicializado", e)
                // Si hay un error al aplicar valores, podría indicar un problema con la instancia
                isInitialized = false
                return ensureInitialized() // Reintentar inicialización
            }
            return true
        }

        if (isInitializing) {
            Log.d(TAG, "TTS ya está en proceso de inicialización")
            return true
        }

        initializeTts()
        return true
    }

    /**
     * Verifica si hay voces disponibles para reproducción
     * @return true si hay al menos una voz disponible, false en caso contrario
     */
    fun areVoicesAvailable(): Boolean {
        // Verificación simple: si hay voces en el mapa, están disponibles
        if (voiceNameMap.isNotEmpty()) {
            Log.d(TAG, "Voces disponibles en caché: ${voiceNameMap.size} voces")
            return true
        }

        // Si no está inicializado, no hay voces disponibles
        if (!isInitialized || textToSpeech == null) {
            Log.d(TAG, "TTS no inicializado correctamente al verificar voces")
            return false
        }

        // Verificación final: comprobar si hay voces en el TTS
        val hasVoices = textToSpeech?.voices?.isNotEmpty() == true
        Log.d(TAG, "Verificación de voces disponibles: $hasVoices")
        return hasVoices
    }

    /**
     * Detiene la reproducción si está activa
     * Útil para llamar desde el ciclo de vida de la actividad (onPause, onStop)
     */
    fun stopPlaybackIfActive() {
        if (isPlaying()) {
            Log.d(TAG, "Deteniendo reproducción desde ciclo de vida")
            stop()
        }
    }



    /**
     * Inicializa el motor TTS con manejo mejorado de errores y compatibilidad
     */
    private fun initializeTts() {
        if (isInitializing || isInitialized) return

        isInitializing = true
        _ttsStateFlow.value = TtsState.Initializing
        Log.d(TAG, "Iniciando inicialización de TTS")

        try {
            textToSpeech = TextToSpeech(context!!) { status ->
                if (status == TextToSpeech.SUCCESS) {
                    // Añadir un pequeño retraso para dar tiempo al sistema
                    Handler(Looper.getMainLooper()).postDelayed({
                        try {
                            // Intentar establecer el idioma español
                            val result = textToSpeech?.setLanguage(currentLocale)

                            when (result) {
                                TextToSpeech.LANG_AVAILABLE, TextToSpeech.LANG_COUNTRY_AVAILABLE -> {
                                    isInitialized = true
                                    isInitializing = false
                                    _ttsStateFlow.value = TtsState.Ready

                                    Log.d(TAG, "TTS inicializado correctamente con idioma ${currentLocale.displayLanguage}")
                                    setupTtsListener()

                                    // Cargar las voces disponibles con un pequeño retraso
                                    Handler(Looper.getMainLooper()).postDelayed({
                                        loadAvailableVoices()
                                    }, 300) // Retraso para dar tiempo al sistema a cargar las voces
                                }
                                TextToSpeech.LANG_MISSING_DATA -> {
                                    Log.e(TAG, "Error: Datos de idioma no disponibles para ${currentLocale.displayLanguage}")

                                    // Intentar con idioma por defecto del sistema
                                    val defaultResult = textToSpeech?.setLanguage(Locale.getDefault())
                                    if (defaultResult == TextToSpeech.LANG_AVAILABLE || defaultResult == TextToSpeech.LANG_COUNTRY_AVAILABLE) {
                                        isInitialized = true
                                        isInitializing = false
                                        currentLocale = Locale.getDefault()
                                        _ttsStateFlow.value = TtsState.Ready

                                        Log.d(TAG, "TTS inicializado con idioma por defecto: ${currentLocale.displayLanguage}")
                                        setupTtsListener()

                                        // Cargar las voces disponibles con un pequeño retraso
                                        Handler(Looper.getMainLooper()).postDelayed({
                                            loadAvailableVoices()
                                        }, 300) // Retraso para dar tiempo al sistema a cargar las voces
                                    } else {
                                        handleInitializationError(TtsError.LANGUAGE_UNAVAILABLE)
                                    }
                                }
                                TextToSpeech.LANG_NOT_SUPPORTED -> {
                                    Log.e(TAG, "Error: Idioma no soportado: ${currentLocale.displayLanguage}")

                                    // Intentar con inglés como último recurso
                                    val englishResult = textToSpeech?.setLanguage(Locale.US)
                                    if (englishResult == TextToSpeech.LANG_AVAILABLE || englishResult == TextToSpeech.LANG_COUNTRY_AVAILABLE) {
                                        isInitialized = true
                                        isInitializing = false
                                        currentLocale = Locale.US
                                        _ttsStateFlow.value = TtsState.Ready

                                        Log.d(TAG, "TTS inicializado con inglés como fallback")
                                        setupTtsListener()

                                        // Cargar las voces disponibles con un pequeño retraso
                                        Handler(Looper.getMainLooper()).postDelayed({
                                            loadAvailableVoices()
                                        }, 300) // Retraso para dar tiempo al sistema a cargar las voces
                                    } else {
                                        handleInitializationError(TtsError.LANGUAGE_UNAVAILABLE)
                                    }
                                }
                                else -> handleInitializationError(TtsError.LANGUAGE_UNAVAILABLE)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "Error durante la configuración de TTS", e)
                            handleInitializationError(TtsError.INITIALIZATION_FAILED)
                        }
                    }, 200) // Retraso para dar tiempo al sistema
                } else {
                    Log.e(TAG, "Error al inicializar TTS: $status")
                    handleInitializationError(TtsError.INITIALIZATION_FAILED)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Excepción al inicializar TTS", e)
            handleInitializationError(TtsError.UNKNOWN_ERROR)
        }
    }

    /**
     * Maneja errores de inicialización del TTS
     */
    private fun handleInitializationError(error: TtsError) {
        isInitialized = false
        isInitializing = false
        _ttsStateFlow.value = when (error) {
            TtsError.LANGUAGE_UNAVAILABLE -> TtsState.LanguageNotSupported
            TtsError.NO_VOICES_AVAILABLE -> TtsState.NoVoicesAvailable
            else -> TtsState.Error
        }

        onTtsError?.invoke(error)
        Log.e(TAG, "Error de TTS: $error")
    }

    /**
     * Carga las voces disponibles para el idioma actual con sistema de fallback jerárquico
     * Simplificado para cargar voces solo en momentos específicos
     */
    private fun loadAvailableVoices() {
        if (textToSpeech == null || !isInitialized) {
            Log.e(TAG, "Error al cargar voces: TTS no inicializado correctamente")
            handleInitializationError(TtsError.INITIALIZATION_FAILED)
            return
        }

        try {
            // Evitar múltiples cargas innecesarias
            if (hasCheckedVoiceSupport && voiceNameMap.isNotEmpty()) {
                Log.d(TAG, "Usando voces en caché (${voiceNameMap.size} voces)")
                onVoicesInitialized?.invoke(availableVoices)
                return
            }

            // Obtener todas las voces disponibles (una sola vez, sin reintentos)
            val allVoices = textToSpeech!!.voices.orEmpty()

            // Limpiar mapa de nombres
            voiceNameMap.clear()

            // Verificar si hay voces disponibles
            if (allVoices.isEmpty()) {
                Log.e(TAG, "No se encontraron voces disponibles en el dispositivo")
                handleInitializationError(TtsError.NO_VOICES_AVAILABLE)
                return
            }

            // Registrar voces disponibles para depuración
            Log.d(TAG, "Voces disponibles en el dispositivo (${allVoices.size}):")
            allVoices.forEach { voice ->
                Log.d(TAG, "Voz: ${voice.name}, Locale: ${voice.locale}, Requiere red: ${voice.isNetworkConnectionRequired}")
            }

            // NIVEL 1: Buscar voces específicas predefinidas
            val specificVoices = allVoices.filter { voice ->
                specificVoiceMap.containsKey(voice.name) && !voice.isNetworkConnectionRequired
            }

            if (specificVoices.isNotEmpty()) {
                Log.d(TAG, "NIVEL 1: Voces específicas encontradas: ${specificVoices.map { it.name }}")
                availableVoices = specificVoices

                // Asignar los nombres amigables a las voces que coinciden, manteniendo el orden
                for ((voiceName, friendlyName) in specificVoiceMap) {
                    val foundVoice = specificVoices.find { it.name == voiceName }
                    if (foundVoice != null) {
                        voiceNameMap[friendlyName] = foundVoice
                        Log.d(TAG, "Voz mapeada: $voiceName -> $friendlyName")
                    }
                }
            } else {
                // NIVEL 2: Buscar cualquier voz en el idioma actual que no requiera conexión
                Log.d(TAG, "NIVEL 2: Buscando voces en ${currentLocale.displayLanguage}")

                val languageVoices = allVoices.filter { voice ->
                    voice.locale.language == currentLocale.language &&
                    !voice.isNetworkConnectionRequired
                }

                if (languageVoices.isNotEmpty()) {
                    Log.d(TAG, "NIVEL 2: Encontradas ${languageVoices.size} voces en ${currentLocale.displayLanguage}")
                    availableVoices = languageVoices

                    // Ordenar por calidad (las que no requieren red primero)
                    val sortedVoices = languageVoices.sortedWith(compareBy(
                        { it.isNetworkConnectionRequired }, // Primero las que no requieren red
                        { it.quality }                      // Luego por calidad
                    ))

                    // Asignar nombres genéricos a las voces
                    sortedVoices.forEachIndexed { index, voice ->
                        val simpleName = "Voz ${index + 1} (${currentLocale.language})"
                        voiceNameMap[simpleName] = voice
                    }
                } else {
                    // NIVEL 3: Buscar voces en español con diferentes códigos de idioma
                    Log.d(TAG, "NIVEL 3: Buscando voces en español con diferentes códigos")

                    // Lista de códigos de idioma que pueden representar español
                    val spanishLanguageCodes = listOf("es", "spa", "spanish", "esp")

                    // Filtrar voces que puedan ser en español
                    val spanishVoices = allVoices.filter { voice ->
                        val langCode = voice.locale.language.lowercase()
                        val voiceName = voice.name.lowercase()

                        // Verificar si el código de idioma es español o si el nombre contiene indicadores de español
                        (spanishLanguageCodes.contains(langCode) ||
                         voiceName.contains("es-") ||
                         voiceName.contains("spa") ||
                         voiceName.contains("spanish") ||
                         voiceName.contains("esp")) &&
                        !voice.isNetworkConnectionRequired
                    }

                    if (spanishVoices.isNotEmpty()) {
                        Log.d(TAG, "NIVEL 3: Encontradas ${spanishVoices.size} voces en español")
                        availableVoices = spanishVoices

                        // Ordenar por calidad
                        val sortedVoices = spanishVoices.sortedWith(compareBy(
                            { it.isNetworkConnectionRequired }, // Primero las que no requieren red
                            { it.quality }                      // Luego por calidad
                        ))

                        // Asignar nombres genéricos
                        sortedVoices.forEachIndexed { index, voice ->
                            val langCode = voice.locale.language
                            val simpleName = "Voz ${index + 1} ($langCode)"
                            voiceNameMap[simpleName] = voice
                        }
                    } else {
                        // NIVEL 4: Usar cualquier voz disponible como último recurso
                        Log.d(TAG, "NIVEL 4: Buscando cualquier voz disponible")

                        val anyVoices = allVoices.filter { !it.isNetworkConnectionRequired }

                        if (anyVoices.isNotEmpty()) {
                            Log.d(TAG, "NIVEL 4: Encontradas ${anyVoices.size} voces disponibles")
                            availableVoices = anyVoices

                            // Asignar nombres genéricos incluyendo el idioma
                            anyVoices.forEachIndexed { index, voice ->
                                val langCode = voice.locale.language
                                val simpleName = "Voz ${index + 1} ($langCode)"
                                voiceNameMap[simpleName] = voice
                            }
                        } else {
                            // NIVEL 5: No hay voces disponibles
                            Log.e(TAG, "NIVEL 5: No se encontraron voces disponibles")
                            handleInitializationError(TtsError.NO_VOICES_AVAILABLE)
                            return
                        }
                    }
                }
            }

            // Establecer la voz predeterminada si no hay una seleccionada
            if (currentVoice == null && voiceNameMap.isNotEmpty()) {
                val firstKey = voiceNameMap.keys.first()
                currentVoice = voiceNameMap[firstKey]
                textToSpeech?.voice = currentVoice
                Log.d(TAG, "Voz predeterminada establecida: ${currentVoice?.name}")
            }

            // Marcar que ya hemos verificado las voces
            hasCheckedVoiceSupport = true

            // Registrar las voces disponibles
            val voiceNames = voiceNameMap.keys.joinToString()
            Log.d(TAG, "Voces disponibles: $voiceNames")

            // Asegurarse de que se apliquen los valores actuales de velocidad y tono
            textToSpeech?.setSpeechRate(currentSpeed)
            textToSpeech?.setPitch(currentPitch)
            Log.d(TAG, "Aplicando configuraciones actuales: velocidad=$currentSpeed, tono=$currentPitch")

            // Notificar que las voces están disponibles
            onVoicesInitialized?.invoke(availableVoices)

        } catch (e: Exception) {
            Log.e(TAG, "Error al cargar las voces disponibles", e)
            handleInitializationError(TtsError.UNKNOWN_ERROR)
        }
    }

    /**
     * Obtiene la lista de voces disponibles con nombres amigables
     */
    open fun getVoiceNameMap(): Map<String, Voice> {
        return voiceNameMap
    }

    /**
     * Obtiene el nombre amigable de la voz actual
     */
    open fun getCurrentVoiceFriendlyName(): String {
        if (currentVoice == null) return "Predeterminada"

        // Buscar el nombre amigable para la voz actual
        for ((friendlyName, voice) in voiceNameMap) {
            if (voice.name == currentVoice?.name) {
                return friendlyName
            }
        }

        return "Voz ${currentVoice?.name?.take(10) ?: "desconocida"}"
    }

    /**
     * Establece la voz según su nombre amigable
     */
    open fun setVoiceByFriendlyName(friendlyName: String) {
        voiceNameMap[friendlyName]?.let { voice ->
            setVoice(voice)
        }
    }

    /**
     * Obtiene la lista de voces disponibles
     */
    open fun getAvailableVoices(): List<Voice> {
        return availableVoices
    }

    /**
     * Obtiene la lista de nombres amigables de voces disponibles
     */
    open fun getAvailableVoiceNames(): List<String> {
        return voiceNameMap.keys.toList()
    }

    /**
     * Obtiene la voz actual
     */
    open fun getCurrentVoice(): Voice? {
        return currentVoice
    }

    /**
     * Establece la voz a utilizar
     */
    open fun setVoice(voice: Voice) {
        if (isInitialized) {
            currentVoice = voice
            textToSpeech?.voice = voice
            Log.d(TAG, "Voz cambiada a: ${voice.name}, requiere internet: ${voice.isNetworkConnectionRequired}")
        }
    }

    /**
     * Verifica si la voz actual requiere conexión a internet
     * @return true si la voz requiere internet, false en caso contrario
     */
    fun currentVoiceRequiresNetwork(): Boolean {
        return currentVoice?.isNetworkConnectionRequired == true
    }

    /**
     * Verifica si el dispositivo tiene conexión a internet
     * @return true si hay conexión a internet, false en caso contrario
     */
    fun isNetworkAvailable(): Boolean {
        if (context == null) return false

        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as? ConnectivityManager
            ?: return false

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false

            return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            return networkInfo != null && networkInfo.isConnected
        }
    }

    /**
     * Configura el listener para seguir el progreso de la lectura
     * Con manejo mejorado de errores y liberación de bloqueos
     */
    private fun setupTtsListener() {
        Log.d(TAG, "Configurando listener de progreso TTS")
        textToSpeech?.setOnUtteranceProgressListener(object : UtteranceProgressListener() {
            override fun onStart(utteranceId: String?) {
                Log.d(TAG, "TTS onStart: utteranceId=$utteranceId")
                if (utteranceId?.startsWith("verse_") == true) {
                    // Guardar el utteranceId activo para filtrar eventos onStop irrelevantes
                    activeUtteranceId = utteranceId

                    currentVerseNumber = utteranceId.substring(6)
                    isPlayingState = true
                    Log.d(TAG, "Iniciando reproducción del versículo: $currentVerseNumber")

                    // Liberar el bloqueo de procesamiento ya que la reproducción ha comenzado
                    isProcessingTtsRequest = false
                    _isProcessingFlow.value = false

                    onPlaybackStarted?.invoke()

                    // Notificar sobre el versículo actual
                    _currentVerseFlow.value = currentVerseNumber
                    onVerseChanged?.invoke(currentVerseNumber!!)
                }
            }

            override fun onDone(utteranceId: String?) {
                Log.d(TAG, "TTS onDone: utteranceId=$utteranceId")

                // Verificar si es el utteranceId activo
                if (utteranceId != activeUtteranceId && utteranceId?.startsWith("verse_") == true) {
                    Log.d(TAG, "Ignorando onDone para utteranceId inactivo: $utteranceId (activo=$activeUtteranceId)")
                    return
                }

                if (pendingVerses.isNotEmpty() && utteranceId?.startsWith("verse_") == true) {
                    // Continuamos con el siguiente versículo
                    Log.d(TAG, "Continuando con el siguiente versículo")
                    speakNextVerse()
                } else if (pendingVerses.isEmpty()) {
                    Log.d(TAG, "Reproducción completada")
                    isPlayingState = false
                    _currentVerseFlow.value = null
                    currentVerseNumber = null
                    activeUtteranceId = null

                    // Asegurar que el bloqueo esté liberado
                    isProcessingTtsRequest = false
                    _isProcessingFlow.value = false

                    // Actualizar el tiempo de la última reproducción para el debounce
                    lastPlaybackTime = System.currentTimeMillis()

                    onPlaybackCompleted?.invoke()
                }
            }

            @Deprecated("Deprecated in Java")
            override fun onError(utteranceId: String?) {
                // Simplificación: solo procesar errores para el utteranceId activo
                if (utteranceId != activeUtteranceId && utteranceId?.startsWith("verse_") == true) {
                    Log.d(TAG, "Ignorando onError para utteranceId inactivo: $utteranceId")
                    return
                }

                // Simplificación: ignorar errores durante detenciones intencionales
                if (isIntentionalStop) {
                    Log.d(TAG, "Ignorando error durante detención intencional: $utteranceId")
                    return
                }

                Log.e(TAG, "TTS onError: utteranceId=$utteranceId")

                // Actualizar estados
                isPlayingState = false
                _currentVerseFlow.value = null
                currentVerseNumber = null
                activeUtteranceId = null
                isProcessingTtsRequest = false
                _isProcessingFlow.value = false
                lastPlaybackTime = System.currentTimeMillis()

                // Notificar detención
                onPlaybackStopped?.invoke()

                // Notificar error solo si es un error real (no una detención intencional)
                onTtsError?.invoke(TtsError.PLAYBACK_ERROR)
            }

            // Implementar métodos adicionales para APIs más recientes
            override fun onStop(utteranceId: String?, interrupted: Boolean) {
                super.onStop(utteranceId, interrupted)

                // Simplificación: solo procesar eventos para el utteranceId activo
                if (utteranceId != activeUtteranceId && utteranceId != null) {
                    return
                }

                Log.d(TAG, "TTS onStop: utteranceId=$utteranceId, interrupted=$interrupted")

                // Solo actualizar estados si fue interrumpido
                if (interrupted) {
                    isPlayingState = false
                    _currentVerseFlow.value = null
                    currentVerseNumber = null
                    activeUtteranceId = null
                    isProcessingTtsRequest = false
                    _isProcessingFlow.value = false
                    lastPlaybackTime = System.currentTimeMillis()

                    onPlaybackStopped?.invoke()
                }
            }
        })
    }

    /**
     * Lee un versículo específico con manejo mejorado de errores
     * Incluye mecanismo de debounce para evitar múltiples reproducciones en corto tiempo
     */
    open fun speakVerse(text: String, verseNumber: String) {
        // Verificar si ya hay una solicitud en proceso
        if (isProcessingTtsRequest) {
            Log.d(TAG, "Ignorando solicitud de reproducción: ya hay una solicitud en proceso")
            return
        }

        // Verificar debounce para evitar múltiples reproducciones en corto tiempo
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastPlaybackTime < debounceTime) {
            Log.d(TAG, "Ignorando solicitud de reproducción (debounce): ${currentTime - lastPlaybackTime}ms desde la última")
            return
        }

        // Actualizar tiempo de última reproducción y activar bloqueo
        lastPlaybackTime = currentTime
        isProcessingTtsRequest = true

        // Actualizar el estado de procesamiento para la UI
        _isProcessingFlow.value = true

        // Asegurar que el TTS esté inicializado
        if (!ensureInitialized()) {
            Log.e(TAG, "No se pudo inicializar TTS para leer versículo")
            onTtsError?.invoke(TtsError.INITIALIZATION_FAILED)
            isProcessingTtsRequest = false
            _isProcessingFlow.value = false
            return
        }

        if (!isInitialized) {
            Log.e(TAG, "TTS no inicializado")
            onTtsError?.invoke(TtsError.INITIALIZATION_FAILED)
            isProcessingTtsRequest = false
            _isProcessingFlow.value = false
            return
        }

        // Verificar si la voz requiere internet y si hay conexión disponible
        if (currentVoiceRequiresNetwork() && !isNetworkAvailable()) {
            Log.e(TAG, "La voz actual requiere conexión a internet, pero no hay conexión disponible")
            onTtsError?.invoke(TtsError.NETWORK_UNAVAILABLE)
            isProcessingTtsRequest = false
            _isProcessingFlow.value = false
            return
        }

        try {
            // Detener reproducción actual y esperar un momento para liberar recursos
            stop()

            // Retraso mínimo para evitar condiciones de carrera
            try {
                Log.d(TAG, "Breve pausa antes de iniciar nueva reproducción")
                Thread.sleep(100) // Reducido de 300ms a 100ms
            } catch (e: InterruptedException) {
                Log.e(TAG, "Interrupción durante la espera después de detener", e)
            }

            val params = Bundle()
            params.putString(TextToSpeech.Engine.KEY_PARAM_UTTERANCE_ID, "verse_$verseNumber")

            // Verificar si la voz actual está disponible, si no usar la primera disponible
            if (currentVoice == null && voiceNameMap.isNotEmpty()) {
                currentVoice = voiceNameMap.values.first()
                textToSpeech?.voice = currentVoice
            }

            // Usar parámetros de audio optimizados
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                params.putFloat(TextToSpeech.Engine.KEY_PARAM_VOLUME, 1.0f) // Volumen máximo
            }

            // Preparar el utteranceId
            val utteranceId = "verse_$verseNumber"

            // Registrar el utteranceId activo antes de iniciar la reproducción
            activeUtteranceId = utteranceId

            Log.d(TAG, "Intentando reproducir versículo: $verseNumber, texto: $text, utteranceId=$utteranceId")
            val result = textToSpeech?.speak(text, TextToSpeech.QUEUE_FLUSH, params, utteranceId)
            Log.d(TAG, "Resultado de speak(): $result (0=SUCCESS, -1=ERROR)")

            if (result == TextToSpeech.ERROR) {
                Log.e(TAG, "Error al iniciar la reproducción del versículo $verseNumber")
                onTtsError?.invoke(TtsError.PLAYBACK_ERROR)
                isProcessingTtsRequest = false
                activeUtteranceId = null
                return
            }

            versesMap = mapOf(verseNumber to text)
            currentVerseNumber = verseNumber
            pendingVerses = emptyList()

            // El bloqueo se liberará en onStart, onDone o onError del UtteranceProgressListener

        } catch (e: Exception) {
            Log.e(TAG, "Excepción al reproducir versículo", e)
            onTtsError?.invoke(TtsError.PLAYBACK_ERROR)
            isProcessingTtsRequest = false
        }
    }

    /**
     * Lee un conjunto de versículos comenzando desde un versículo específico
     * con manejo mejorado de errores y compatibilidad
     * Incluye mecanismo de debounce para evitar múltiples reproducciones en corto tiempo
     */
    open fun speakVerses(verses: Map<String, String>, startVerseNumber: String) {
        // Verificar si ya hay una solicitud en proceso
        if (isProcessingTtsRequest) {
            Log.d(TAG, "Ignorando solicitud de reproducción: ya hay una solicitud en proceso")
            return
        }

        // Verificar debounce para evitar múltiples reproducciones en corto tiempo
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastPlaybackTime < debounceTime) {
            Log.d(TAG, "Ignorando solicitud de reproducción (debounce): ${currentTime - lastPlaybackTime}ms desde la última")
            return
        }

        // Actualizar tiempo de última reproducción y activar bloqueo
        lastPlaybackTime = currentTime
        isProcessingTtsRequest = true

        // Actualizar el estado de procesamiento para la UI
        _isProcessingFlow.value = true

        // Asegurar que el TTS esté inicializado
        if (!ensureInitialized()) {
            Log.e(TAG, "No se pudo inicializar TTS para leer versículos")
            onTtsError?.invoke(TtsError.INITIALIZATION_FAILED)
            isProcessingTtsRequest = false
            _isProcessingFlow.value = false
            return
        }

        if (!isInitialized) {
            Log.e(TAG, "TTS no inicializado")
            onTtsError?.invoke(TtsError.INITIALIZATION_FAILED)
            isProcessingTtsRequest = false
            _isProcessingFlow.value = false
            return
        }

        // Verificar si hay voces disponibles
        if (textToSpeech?.voices?.isEmpty() == true || voiceNameMap.isEmpty()) {
            Log.e(TAG, "No hay voces disponibles para reproducción")
            onTtsError?.invoke(TtsError.NO_VOICES_AVAILABLE)
            isProcessingTtsRequest = false
            _isProcessingFlow.value = false
            return
        }

        // Verificar si la voz requiere internet y si hay conexión disponible
        if (currentVoiceRequiresNetwork() && !isNetworkAvailable()) {
            Log.e(TAG, "La voz actual requiere conexión a internet, pero no hay conexión disponible")
            onTtsError?.invoke(TtsError.NETWORK_UNAVAILABLE)
            isProcessingTtsRequest = false
            _isProcessingFlow.value = false
            return
        }

        try {
            // Detener reproducción actual y esperar un momento para liberar recursos
            stop()

            // Retraso mínimo para evitar condiciones de carrera
            try {
                Log.d(TAG, "Breve pausa antes de iniciar nueva reproducción")
                Thread.sleep(100) // Reducido de 300ms a 100ms
            } catch (e: InterruptedException) {
                Log.e(TAG, "Interrupción durante la espera después de detener", e)
            }

            versesMap = verses

            // Ordenamos los versículos numéricamente
            val sortedVerses = verses.toSortedMap { a, b -> a.toInt().compareTo(b.toInt()) }

            // Encontrar el índice del versículo inicial
            val startIndex = sortedVerses.keys.indexOfFirst { it == startVerseNumber }
            if (startIndex == -1) {
                Log.e(TAG, "No se encontró el versículo inicial: $startVerseNumber")
                onTtsError?.invoke(TtsError.PLAYBACK_ERROR)
                isProcessingTtsRequest = false
                return
            }

            // Verificar si la voz actual está disponible, si no usar la primera disponible
            if (currentVoice == null && voiceNameMap.isNotEmpty()) {
                currentVoice = voiceNameMap.values.first()
                textToSpeech?.voice = currentVoice
            }

            // Leer desde el versículo inicial hasta el final
            var isFirst = true
            var hasError = false

            sortedVerses.entries.drop(startIndex).forEach { (number, text) ->
                if (hasError) return@forEach

                val utteranceId = "verse_$number"
                val params = Bundle()
                params.putString(TextToSpeech.Engine.KEY_PARAM_UTTERANCE_ID, utteranceId)

                // Usar parámetros de audio optimizados
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    params.putFloat(TextToSpeech.Engine.KEY_PARAM_VOLUME, 1.0f) // Volumen máximo
                }

                // El primer versículo limpia la cola, los demás se añaden a la cola
                val queueMode = if (isFirst) TextToSpeech.QUEUE_FLUSH else TextToSpeech.QUEUE_ADD

                // Si es el primer versículo, registrar el utteranceId activo
                if (isFirst) {
                    activeUtteranceId = utteranceId
                    Log.d(TAG, "Registrando utteranceId activo para reproducción: $utteranceId")
                }

                val result = textToSpeech?.speak(text, queueMode, params, utteranceId)

                if (result == TextToSpeech.ERROR) {
                    Log.e(TAG, "Error al añadir versículo $number a la cola")
                    hasError = true
                    onTtsError?.invoke(TtsError.PLAYBACK_ERROR)
                    isProcessingTtsRequest = false
                    if (isFirst) {
                        activeUtteranceId = null
                    }
                    return@forEach
                }

                if (isFirst) {
                    onVerseChanged?.invoke(number)
                    isFirst = false
                }
            }

            if (!hasError) {
                isPlayingState = true
            } else {
                isProcessingTtsRequest = false
            }

            // El bloqueo se liberará en onStart, onDone o onError del UtteranceProgressListener

        } catch (e: Exception) {
            Log.e(TAG, "Excepción al reproducir versículos", e)
            onTtsError?.invoke(TtsError.PLAYBACK_ERROR)
            isProcessingTtsRequest = false
        }
    }

    /**
     * Detiene la reproducción actual con manejo simplificado
     * Mantiene solo la funcionalidad esencial para una respuesta más rápida
     */
    open fun stop() {
        try {
            // Marcar como detención intencional (simplificado)
            isIntentionalStop = true
            Log.d(TAG, "Deteniendo reproducción, utteranceId=$activeUtteranceId")

            if (isPlaying()) {
                // Detener la reproducción
                textToSpeech?.stop()

                // Actualizar estados inmediatamente
                isPlayingState = false
                _currentVerseFlow.value = null

                // Liberar el bloqueo de procesamiento
                isProcessingTtsRequest = false
                _isProcessingFlow.value = false

                // Actualizar el tiempo de la última reproducción para el debounce
                lastPlaybackTime = System.currentTimeMillis()

                // Notificar que se detuvo la reproducción
                onPlaybackStopped?.invoke()
            }

            // Resetear la marca de detención intencional después de un breve tiempo
            Handler(Looper.getMainLooper()).postDelayed({
                isIntentionalStop = false
                Log.d(TAG, "Marca de detención intencional reseteada")
            }, intentionalStopResetTime)

        } catch (e: Exception) {
            Log.e(TAG, "Error al detener la reproducción", e)
            // Asegurar que se liberen los bloqueos incluso en caso de error
            isProcessingTtsRequest = false
            isIntentionalStop = false
        }
    }

    /**
     * Ajusta la velocidad de reproducción con validación
     */
    open fun setSpeed(speed: Float) {
        if (!ensureInitialized()) return

        try {
            // Limitar a un rango seguro
            val safeSpeed = speed.coerceIn(0.5f, 2.0f)

            if (isInitialized) {
                currentSpeed = safeSpeed
                textToSpeech?.setSpeechRate(safeSpeed)
                Log.d(TAG, "Velocidad ajustada a: $safeSpeed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error al ajustar la velocidad", e)
        }
    }

    /**
     * Ajusta el tono de la voz con validación
     */
    open fun setPitch(pitch: Float) {
        if (!ensureInitialized()) return

        try {
            // Limitar a un rango seguro
            val safePitch = pitch.coerceIn(0.5f, 2.0f)

            if (isInitialized) {
                currentPitch = safePitch
                textToSpeech?.setPitch(safePitch)
                Log.d(TAG, "Tono ajustado a: $safePitch")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error al ajustar el tono", e)
        }
    }

    /**
     * Cambia el idioma de reproducción con manejo de errores
     */
    fun setLanguage(languageCode: String, countryCode: String) {
        if (!ensureInitialized()) {
            onTtsError?.invoke(TtsError.INITIALIZATION_FAILED)
            return
        }

        try {
            val locale = Locale(languageCode, countryCode)
            val result = textToSpeech?.setLanguage(locale)

            when (result) {
                TextToSpeech.LANG_AVAILABLE, TextToSpeech.LANG_COUNTRY_AVAILABLE -> {
                    currentLocale = locale
                    // Recargar las voces para el nuevo idioma
                    hasCheckedVoiceSupport = false // Forzar recarga de voces
                    loadAvailableVoices()
                    Log.d(TAG, "Idioma cambiado a: ${locale.displayLanguage}")
                }
                TextToSpeech.LANG_MISSING_DATA -> {
                    Log.e(TAG, "Faltan datos para el idioma: $languageCode-$countryCode")
                    onTtsError?.invoke(TtsError.LANGUAGE_UNAVAILABLE)
                }
                TextToSpeech.LANG_NOT_SUPPORTED -> {
                    Log.e(TAG, "Idioma no soportado: $languageCode-$countryCode")
                    onTtsError?.invoke(TtsError.LANGUAGE_UNAVAILABLE)
                }
                else -> {
                    Log.e(TAG, "Error desconocido al cambiar idioma: $languageCode-$countryCode")
                    onTtsError?.invoke(TtsError.UNKNOWN_ERROR)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Excepción al cambiar idioma", e)
            onTtsError?.invoke(TtsError.UNKNOWN_ERROR)
        }
    }

    /**
     * Verifica si está en reproducción
     */
    open fun isPlaying(): Boolean {
        return isPlayingState
    }

    /**
     * Obtiene la velocidad actual
     */
    open fun getCurrentSpeed(): Float {
        return currentSpeed
    }

    /**
     * Obtiene el tono actual
     */
    open fun getCurrentPitch(): Float {
        return currentPitch
    }

    /**
     * Libera recursos cuando ya no se necesita
     */
    open fun shutdown() {
        try {
            stop()
            textToSpeech?.shutdown()
            textToSpeech = null
            isInitialized = false
            isInitializing = false
            hasCheckedVoiceSupport = false
            _ttsStateFlow.value = TtsState.NotInitialized
            Log.d(TAG, "TTS liberado correctamente")
        } catch (e: Exception) {
            Log.e(TAG, "Error al liberar recursos de TTS", e)
        }
    }

    /**
     * Reinicia el motor TTS en caso de problemas
     * Carga explícitamente las voces después de la inicialización
     * @return true si se reinició correctamente
     */
    fun restart(): Boolean {
        try {
            // Guardar los valores actuales y la voz antes de reiniciar
            val savedSpeed = currentSpeed
            val savedPitch = currentPitch
            val savedVoice = currentVoice
            val savedVoiceName = if (savedVoice != null) {
                voiceNameMap.entries.find { it.value == savedVoice }?.key
            } else null

            Log.d(TAG, "Guardando estado antes de reiniciar: velocidad=$savedSpeed, tono=$savedPitch, voz=${savedVoiceName ?: "predeterminada"}")

            // Liberar recursos completamente
            shutdown()

            // Breve pausa para asegurar liberación de recursos
            Thread.sleep(300)

            // Reinicializar el TTS
            isInitialized = false
            isInitializing = false
            hasCheckedVoiceSupport = false
            val result = ensureInitialized()

            if (result) {
                // Esperar a que la inicialización se complete
                var attempts = 0
                while (!isInitialized && attempts < 15) {
                    Thread.sleep(200)
                    attempts++
                }

                // Forzar la carga de voces explícitamente
                if (isInitialized) {
                    Log.d(TAG, "Cargando voces explícitamente después del reinicio")
                    // Usar un Handler para asegurar que se ejecute en el hilo principal
                    Handler(Looper.getMainLooper()).post {
                        loadAvailableVoices()
                    }

                    // Esperar a que las voces se carguen
                    Thread.sleep(300)
                }

                // Restaurar los valores guardados
                setSpeed(savedSpeed)
                setPitch(savedPitch)

                // Restaurar la voz si es posible
                if (savedVoiceName != null) {
                    val voice = voiceNameMap[savedVoiceName]
                    if (voice != null) {
                        setVoice(voice)
                        Log.d(TAG, "Voz restaurada: $savedVoiceName")
                    }
                }

                Log.d(TAG, "TTS reiniciado correctamente. Configuraciones restauradas.")
            }

            return result
        } catch (e: Exception) {
            Log.e(TAG, "Error al reiniciar TTS", e)
            return false
        }
    }

    /**
     * Habla el siguiente versículo de la cola
     * Con manejo mejorado del utteranceId activo
     */
    private fun speakNextVerse() {
        if (pendingVerses.isEmpty() || !isInitialized) return

        val nextVerseNumber = pendingVerses.first()
        pendingVerses = pendingVerses.drop(1)

        val text = versesMap[nextVerseNumber] ?: return

        // Preparar el utteranceId
        val utteranceId = "verse_$nextVerseNumber"

        // Actualizar el utteranceId activo
        activeUtteranceId = utteranceId
        Log.d(TAG, "Actualizando utteranceId activo para siguiente versículo: $utteranceId")

        val params = Bundle()
        params.putString(TextToSpeech.Engine.KEY_PARAM_UTTERANCE_ID, utteranceId)

        textToSpeech?.speak(text, TextToSpeech.QUEUE_FLUSH, params, utteranceId)
        currentVerseNumber = nextVerseNumber

        // El bloqueo se liberará en onStart, onDone o onError del UtteranceProgressListener
    }

    companion object {
        private const val TAG = "TtsManager"
    }
}