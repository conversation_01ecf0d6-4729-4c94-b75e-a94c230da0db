-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:2:1-32:12
INJECTED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:2:1-32:12
INJECTED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:2:1-32:12
INJECTED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:2:1-32:12
MERGED from [androidx.compose.material3:material3:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\084a2c6004ec8b69e4406c583675f216\transformed\material3-1.1.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\99f83d1afa347ecb50975f39f2eeda0f\transformed\navigation-common-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\7601f88b2cef2a4a7125135068095837\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\a47f1f0493dacb8586b8f57f90a40dc6\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\0f6a288dab6e761c254a314788bc39e6\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\1e2870e258ae9bdfaf354dbe3cc41ed7\transformed\navigation-compose-2.7.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-pager-indicators:0.28.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\664fd9669ea7a1a017c6eba3beb19e62\transformed\accompanist-pager-indicators-0.28.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\f95a3092c14e995e5e2cc4d41235634f\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\543637b51b285d2160c34c34159f8639\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\591869767d1449cdbe9a04ea122ff4e9\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-pager:0.28.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ca54f0066a6fb5a73f2f901f4124a1ba\transformed\accompanist-pager-0.28.0\AndroidManifest.xml:17:1-24:12
MERGED from [dev.chrisbanes.snapper:snapper:0.2.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\2810ccc8fb7c39abd375ff249b4b1e06\transformed\snapper-0.2.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c31445d316cfe01b85e1b8d99792b226\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c26a534cbbe7c772b6f505e0e3190b39\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\f542eb7f011c7ab80600b23c7e711e47\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e994d3732c8464cfc7d69fdf073dbb51\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ebe1134789bbba29d939dd1accb7dc11\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b9aad071966bf40d3edc18d4e11044ea\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5378d873a5e349b52846493db02fa27f\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e351c8c4453569c5f3e862dec9e425f2\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ca6f09089290932ceab99c9bb6dceb03\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\034011938d3a54091fc760f490a5967e\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\860fd76c880d9e09a864fdbfcc0048a7\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\000b50f14e049fbc1d74c82d2addcd26\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\0704e9f78fcf4a6269c1a8c714a67d30\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\6d9a30476eb0a9d76f1baf7d3f58eb97\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\60099eca14cc72c8f59ff73e44833067\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\8aaae08d45cb5881a1e1bfeb28e9651e\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\891ad0b053c37baec119ea42386a0646\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\47e898b8eb3a44112a04b2680678ab97\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\96b7aae757b73e215923b41989b590bc\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\0f327f3b2e33f04366e0af4c09726a8d\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a3370859da8a71140dbc829015253760\transformed\lifecycle-viewmodel-compose-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e8519d4f7af77f31265f93c93332bfd6\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd0de47c6caf39a023a0e1f57bbb36ce\transformed\ui-test-manifest-1.5.1\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\216db8d0a30175238f1d931ff9b405d8\transformed\activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\7b6b32920fccd721c35aae9bcfcb5e95\transformed\activity-compose-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\20a1198831e080a25dca3a6093ad3391\transformed\activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\51cf6b2c8dd9298f5d541d6fb0952380\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\4d6fdd610d3d4d5b492d9d2ff75a4d97\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\f8d849befe50157120c39d99793dc7a6\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\d581dbda262f0aafb7c2311e84ff7a40\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\4ac3c906789459b11baa3751b8dba256\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b34e7cb2b7fefd10f3aa9f92d7d51e96\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\1a260e50a03d6daae2a4d34c189d3978\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e98a1369f4b667e3cd7d853a91d0f3fc\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\1aaf67bb4f06c1a78eebd37200f8ebee\transformed\napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e09c2fcff7559595e5932c1aae2dc6a9\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\6e89ec779fa057e2355ef510a36c07c8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\7cff2b7829cff570ba567934fbbd1e59\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\9a20719684b6f7dd9e6d4ef09eed6e5d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ca3b4cd80dd4bec26bbfdabea04f2cbe\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:7:5-79
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:7:22-76
application
ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:9:5-30:19
INJECTED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:9:5-30:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\860fd76c880d9e09a864fdbfcc0048a7\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\860fd76c880d9e09a864fdbfcc0048a7\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\6d9a30476eb0a9d76f1baf7d3f58eb97\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\6d9a30476eb0a9d76f1baf7d3f58eb97\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd0de47c6caf39a023a0e1f57bbb36ce\transformed\ui-test-manifest-1.5.1\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd0de47c6caf39a023a0e1f57bbb36ce\transformed\ui-test-manifest-1.5.1\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\6e89ec779fa057e2355ef510a36c07c8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\6e89ec779fa057e2355ef510a36c07c8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\9a20719684b6f7dd9e6d4ef09eed6e5d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\9a20719684b6f7dd9e6d4ef09eed6e5d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:16:9-35
	android:label
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:14:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:12:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:15:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:18:9-29
	android:icon
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:13:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:10:9-35
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:17:9-48
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:11:9-65
activity#com.develop.mybible.MainActivity
ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:19:9-29:20
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:23:13-55
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:21:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:22:13-87
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:24:13-52
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:20:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:25:13-28:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:26:17-69
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:26:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:27:17-77
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml:27:27-74
uses-sdk
INJECTED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml
MERGED from [androidx.compose.material3:material3:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\084a2c6004ec8b69e4406c583675f216\transformed\material3-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\084a2c6004ec8b69e4406c583675f216\transformed\material3-1.1.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\99f83d1afa347ecb50975f39f2eeda0f\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\99f83d1afa347ecb50975f39f2eeda0f\transformed\navigation-common-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\7601f88b2cef2a4a7125135068095837\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\7601f88b2cef2a4a7125135068095837\transformed\navigation-runtime-2.7.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\a47f1f0493dacb8586b8f57f90a40dc6\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\a47f1f0493dacb8586b8f57f90a40dc6\transformed\navigation-common-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\0f6a288dab6e761c254a314788bc39e6\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\0f6a288dab6e761c254a314788bc39e6\transformed\navigation-runtime-ktx-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\1e2870e258ae9bdfaf354dbe3cc41ed7\transformed\navigation-compose-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\1e2870e258ae9bdfaf354dbe3cc41ed7\transformed\navigation-compose-2.7.5\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-pager-indicators:0.28.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\664fd9669ea7a1a017c6eba3beb19e62\transformed\accompanist-pager-indicators-0.28.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.accompanist:accompanist-pager-indicators:0.28.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\664fd9669ea7a1a017c6eba3beb19e62\transformed\accompanist-pager-indicators-0.28.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material:material-ripple-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\f95a3092c14e995e5e2cc4d41235634f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\f95a3092c14e995e5e2cc4d41235634f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\543637b51b285d2160c34c34159f8639\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\543637b51b285d2160c34c34159f8639\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\591869767d1449cdbe9a04ea122ff4e9\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\591869767d1449cdbe9a04ea122ff4e9\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-pager:0.28.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ca54f0066a6fb5a73f2f901f4124a1ba\transformed\accompanist-pager-0.28.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.accompanist:accompanist-pager:0.28.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ca54f0066a6fb5a73f2f901f4124a1ba\transformed\accompanist-pager-0.28.0\AndroidManifest.xml:20:5-22:41
MERGED from [dev.chrisbanes.snapper:snapper:0.2.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\2810ccc8fb7c39abd375ff249b4b1e06\transformed\snapper-0.2.2\AndroidManifest.xml:20:5-22:41
MERGED from [dev.chrisbanes.snapper:snapper:0.2.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\2810ccc8fb7c39abd375ff249b4b1e06\transformed\snapper-0.2.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c31445d316cfe01b85e1b8d99792b226\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c31445d316cfe01b85e1b8d99792b226\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c26a534cbbe7c772b6f505e0e3190b39\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c26a534cbbe7c772b6f505e0e3190b39\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\f542eb7f011c7ab80600b23c7e711e47\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\f542eb7f011c7ab80600b23c7e711e47\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e994d3732c8464cfc7d69fdf073dbb51\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e994d3732c8464cfc7d69fdf073dbb51\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ebe1134789bbba29d939dd1accb7dc11\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ebe1134789bbba29d939dd1accb7dc11\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b9aad071966bf40d3edc18d4e11044ea\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b9aad071966bf40d3edc18d4e11044ea\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5378d873a5e349b52846493db02fa27f\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\5378d873a5e349b52846493db02fa27f\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e351c8c4453569c5f3e862dec9e425f2\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e351c8c4453569c5f3e862dec9e425f2\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ca6f09089290932ceab99c9bb6dceb03\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\ca6f09089290932ceab99c9bb6dceb03\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\034011938d3a54091fc760f490a5967e\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\034011938d3a54091fc760f490a5967e\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\860fd76c880d9e09a864fdbfcc0048a7\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\860fd76c880d9e09a864fdbfcc0048a7\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\000b50f14e049fbc1d74c82d2addcd26\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\000b50f14e049fbc1d74c82d2addcd26\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\0704e9f78fcf4a6269c1a8c714a67d30\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\0704e9f78fcf4a6269c1a8c714a67d30\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\6d9a30476eb0a9d76f1baf7d3f58eb97\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\6d9a30476eb0a9d76f1baf7d3f58eb97\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\60099eca14cc72c8f59ff73e44833067\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\60099eca14cc72c8f59ff73e44833067\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\8aaae08d45cb5881a1e1bfeb28e9651e\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\8aaae08d45cb5881a1e1bfeb28e9651e\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\891ad0b053c37baec119ea42386a0646\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\891ad0b053c37baec119ea42386a0646\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\47e898b8eb3a44112a04b2680678ab97\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\47e898b8eb3a44112a04b2680678ab97\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\96b7aae757b73e215923b41989b590bc\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\96b7aae757b73e215923b41989b590bc\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\0f327f3b2e33f04366e0af4c09726a8d\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\0f327f3b2e33f04366e0af4c09726a8d\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a3370859da8a71140dbc829015253760\transformed\lifecycle-viewmodel-compose-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a3370859da8a71140dbc829015253760\transformed\lifecycle-viewmodel-compose-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e8519d4f7af77f31265f93c93332bfd6\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\e8519d4f7af77f31265f93c93332bfd6\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd0de47c6caf39a023a0e1f57bbb36ce\transformed\ui-test-manifest-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd0de47c6caf39a023a0e1f57bbb36ce\transformed\ui-test-manifest-1.5.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\216db8d0a30175238f1d931ff9b405d8\transformed\activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\216db8d0a30175238f1d931ff9b405d8\transformed\activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\7b6b32920fccd721c35aae9bcfcb5e95\transformed\activity-compose-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\7b6b32920fccd721c35aae9bcfcb5e95\transformed\activity-compose-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\20a1198831e080a25dca3a6093ad3391\transformed\activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\20a1198831e080a25dca3a6093ad3391\transformed\activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\51cf6b2c8dd9298f5d541d6fb0952380\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\51cf6b2c8dd9298f5d541d6fb0952380\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\4d6fdd610d3d4d5b492d9d2ff75a4d97\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\4d6fdd610d3d4d5b492d9d2ff75a4d97\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\f8d849befe50157120c39d99793dc7a6\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\f8d849befe50157120c39d99793dc7a6\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\d581dbda262f0aafb7c2311e84ff7a40\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\d581dbda262f0aafb7c2311e84ff7a40\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\4ac3c906789459b11baa3751b8dba256\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\4ac3c906789459b11baa3751b8dba256\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b34e7cb2b7fefd10f3aa9f92d7d51e96\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b34e7cb2b7fefd10f3aa9f92d7d51e96\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\1a260e50a03d6daae2a4d34c189d3978\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\1a260e50a03d6daae2a4d34c189d3978\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e98a1369f4b667e3cd7d853a91d0f3fc\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e98a1369f4b667e3cd7d853a91d0f3fc\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\1aaf67bb4f06c1a78eebd37200f8ebee\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\1aaf67bb4f06c1a78eebd37200f8ebee\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e09c2fcff7559595e5932c1aae2dc6a9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e09c2fcff7559595e5932c1aae2dc6a9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\6e89ec779fa057e2355ef510a36c07c8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\6e89ec779fa057e2355ef510a36c07c8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\7cff2b7829cff570ba567934fbbd1e59\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\7cff2b7829cff570ba567934fbbd1e59\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\9a20719684b6f7dd9e6d4ef09eed6e5d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\9a20719684b6f7dd9e6d4ef09eed6e5d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ca3b4cd80dd4bec26bbfdabea04f2cbe\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ca3b4cd80dd4bec26bbfdabea04f2cbe\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\OneDrive\Documentos\Android Studio (Projects)\MyBible\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\860fd76c880d9e09a864fdbfcc0048a7\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\860fd76c880d9e09a864fdbfcc0048a7\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\860fd76c880d9e09a864fdbfcc0048a7\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\6d9a30476eb0a9d76f1baf7d3f58eb97\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\6d9a30476eb0a9d76f1baf7d3f58eb97\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\9a20719684b6f7dd9e6d4ef09eed6e5d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\9a20719684b6f7dd9e6d4ef09eed6e5d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e9473c30bcc379d68290397687a0115a\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\6d9a30476eb0a9d76f1baf7d3f58eb97\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\6d9a30476eb0a9d76f1baf7d3f58eb97\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\6d9a30476eb0a9d76f1baf7d3f58eb97\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd0de47c6caf39a023a0e1f57bbb36ce\transformed\ui-test-manifest-1.5.1\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd0de47c6caf39a023a0e1f57bbb36ce\transformed\ui-test-manifest-1.5.1\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd0de47c6caf39a023a0e1f57bbb36ce\transformed\ui-test-manifest-1.5.1\AndroidManifest.xml:24:13-63
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.develop.mybible.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.develop.mybible.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\039f90c30800cf165db0f833f096a0f1\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a6fb739262b298029efa7820dcf579b6\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
