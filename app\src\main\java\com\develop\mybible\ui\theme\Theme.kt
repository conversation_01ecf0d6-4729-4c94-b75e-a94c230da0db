package com.develop.mybible.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

// Definición de esquemas de colores personalizados para la aplicación de Biblia
private val DarkColorScheme = darkColorScheme(
    primary = BiblePrimary,
    onPrimary = BibleOnPrimary,
    primaryContainer = BiblePrimaryContainer,
    onPrimaryContainer = BibleOnPrimaryContainer,
    secondary = BibleSecondary,
    onSecondary = BibleOnSecondary,
    secondaryContainer = BibleSecondaryContainer,
    onSecondaryContainer = BibleOnSecondaryContainer,
    tertiary = BibleTertiary,
    onTertiary = BibleOnTertiary,
    background = BibleDarkBackground,
    onBackground = BibleOnDarkBackground,
    surface = BibleDarkSurface,
    onSurface = BibleOnDarkSurface
)

private val LightColorScheme = lightColorScheme(
    primary = BiblePrimary,
    onPrimary = BibleOnPrimary,
    primaryContainer = BiblePrimaryContainer,
    onPrimaryContainer = BibleOnPrimaryContainer,
    secondary = BibleSecondary,
    onSecondary = BibleOnSecondary,
    secondaryContainer = BibleSecondaryContainer,
    onSecondaryContainer = BibleOnSecondaryContainer,
    tertiary = BibleTertiary,
    onTertiary = BibleOnTertiary,
    background = BibleLightBackground,
    onBackground = BibleOnLightBackground,
    surface = BibleLightSurface,
    onSurface = BibleOnLightSurface
)

@Composable
fun OpenBibliaTheme(
    darkTheme: Boolean = true, // Tema oscuro por defecto en lugar de depender del tema del sistema
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = false,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }
        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            // Usar colores diferentes para la barra de estado según el tema
            window.statusBarColor = if (darkTheme) {
                // En modo oscuro, usar el color de fondo oscuro para la barra de estado
                colorScheme.surface.toArgb()
            } else {
                // En modo claro, usar blanco o un color claro para la barra de estado
                colorScheme.surface.toArgb()
            }

            // Configurar el color de los iconos de la barra de estado
            // En modo claro, los iconos deben ser oscuros; en modo oscuro, deben ser claros
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = !darkTheme
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = BibleTypography,
        content = content
    )
}