package com.develop.mybible.audio

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * Componente de controles de audio para TTS
 * Con indicador visual de estado durante el debounce
 */
@Composable
fun AudioControls(
    isPlaying: Boolean,
    currentSpeed: Float,
    currentPitch: Float,
    onPlayPauseClick: () -> Unit,
    onStopClick: () -> Unit,
    onSpeedChanged: (Float) -> Unit,
    onPitchChanged: (Float) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    isProcessing: Boolean = false // Nuevo parámetro para indicar si está procesando una acción
) {
    var showSettings by remember { mutableStateOf(false) }

    // Estado para mostrar un indicador visual durante el debounce
    var showDebounceIndicator by remember { mutableStateOf(false) }

    // Efecto para mostrar el indicador de debounce cuando se hace clic en reproducir/pausar
    LaunchedEffect(isProcessing) {
        showDebounceIndicator = isProcessing
        if (isProcessing) {
            // Ocultar el indicador después de 300ms (tiempo de debounce)
            kotlinx.coroutines.delay(300)
            showDebounceIndicator = false
        }
    }

    Card(
        modifier = modifier
            .fillMaxWidth()
            .shadow(
                elevation = 4.dp,
                shape = RoundedCornerShape(16.dp)
            ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Controles principales
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Botón de reproducir/pausar con indicador visual de debounce
                Box(
                    contentAlignment = Alignment.Center
                ) {
                    // Botón principal
                    IconButton(
                        onClick = onPlayPauseClick,
                        enabled = (enabled || isPlaying) && !showDebounceIndicator, // Deshabilitar durante el debounce
                        modifier = Modifier
                            .size(48.dp)
                            .background(
                                when {
                                    showDebounceIndicator -> MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
                                    enabled || isPlaying -> MaterialTheme.colorScheme.primary
                                    else -> MaterialTheme.colorScheme.primary.copy(alpha = 0.5f)
                                },
                                CircleShape
                            )
                    ) {
                        Icon(
                            imageVector = if (isPlaying) Icons.Filled.Pause else Icons.Filled.PlayArrow,
                            contentDescription = if (isPlaying) "Pausar" else "Reproducir",
                            tint = MaterialTheme.colorScheme.onPrimary.copy(
                                alpha = when {
                                    showDebounceIndicator -> 0.5f
                                    enabled || isPlaying -> 1f
                                    else -> 0.5f
                                }
                            ),
                            modifier = Modifier.size(28.dp)
                        )
                    }

                    // Indicador de procesamiento (visible solo durante el debounce)
                    if (showDebounceIndicator) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(48.dp),
                            color = MaterialTheme.colorScheme.primary,
                            strokeWidth = 2.dp
                        )
                    }
                }

                // Botón de detener
                IconButton(
                    onClick = onStopClick,
                    enabled = isPlaying, // Solo habilitar si está reproduciendo
                    modifier = Modifier
                        .size(48.dp)
                        .background(
                            if (isPlaying)
                                MaterialTheme.colorScheme.primaryContainer
                            else
                                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.5f),
                            CircleShape
                        )
                ) {
                    Icon(
                        imageVector = Icons.Filled.Stop,
                        contentDescription = "Detener",
                        tint = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = if (isPlaying) 1f else 0.5f),
                        modifier = Modifier.size(28.dp)
                    )
                }

                // Botón de configuración
                IconButton(
                    onClick = { showSettings = !showSettings },
                    enabled = enabled || isPlaying, // Permitir configurar si está habilitado o reproduciendo
                    modifier = Modifier
                        .size(48.dp)
                        .background(
                            if (enabled || isPlaying)
                                MaterialTheme.colorScheme.secondaryContainer
                            else
                                MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.5f),
                            CircleShape
                        )
                ) {
                    Icon(
                        imageVector = Icons.Filled.Settings,
                        contentDescription = "Configuración",
                        tint = MaterialTheme.colorScheme.onSecondaryContainer.copy(alpha = if (enabled || isPlaying) 1f else 0.5f),
                        modifier = Modifier.size(28.dp)
                    )
                }
            }

            // Panel de configuración expandible
            AnimatedVisibility(
                visible = showSettings,
                enter = expandVertically(),
                exit = shrinkVertically()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp)
                ) {
                    // Control de velocidad
                    Text(
                        text = "Velocidad: ${String.format("%.1f", currentSpeed)}x",
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp,
                        color = if (enabled || isPlaying)
                            MaterialTheme.colorScheme.onSurface
                        else
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                    )

                    Slider(
                        value = currentSpeed,
                        onValueChange = onSpeedChanged,
                        valueRange = 0.5f..2.0f,
                        steps = 14, // 15 posiciones entre 0.5 y 2.0
                        modifier = Modifier.padding(vertical = 8.dp),
                        enabled = enabled || isPlaying
                    )

                    // Control de tono
                    Text(
                        text = "Tono: ${String.format("%.1f", currentPitch)}x",
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp,
                        color = if (enabled || isPlaying)
                            MaterialTheme.colorScheme.onSurface
                        else
                            MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                    )

                    Slider(
                        value = currentPitch,
                        onValueChange = onPitchChanged,
                        valueRange = 0.5f..2.0f,
                        steps = 14, // 15 posiciones entre 0.5 y 2.0
                        modifier = Modifier.padding(vertical = 8.dp),
                        enabled = enabled || isPlaying
                    )
                }
            }
        }
    }
}